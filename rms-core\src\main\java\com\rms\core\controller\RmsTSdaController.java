package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSda;
import com.rms.core.service.IRmsTSdaService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 药品说明书Controller
 *
 * <AUTHOR>
 * @date 2025-07-06
 */
@RestController
@RequestMapping("/rms/sda")
public class RmsTSdaController extends BaseController
{
    @Autowired
    private IRmsTSdaService rmsTSdaService;

    /**
     * 查询药品说明书列表
     */
    @PreAuthorize("@ss.hasPermi('rms:sda:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSda rmsTSda)
    {
        startPage();
        List<RmsTSda> list = rmsTSdaService.selectRmsTSdaList(rmsTSda);
        return getDataTable(list);
    }

    /**
     * 导出药品说明书列表
     */
    @PreAuthorize("@ss.hasPermi('rms:sda:export')")
    @Log(title = "药品说明书", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSda rmsTSda)
    {
        List<RmsTSda> list = rmsTSdaService.selectRmsTSdaList(rmsTSda);
        ExcelUtil<RmsTSda> util = new ExcelUtil<RmsTSda>(RmsTSda.class);
        util.exportExcel(response, list, "药品说明书数据");
    }

    /**
     * 获取药品说明书详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:sda:query')")
    @GetMapping(value = "/{ID}")
    public AjaxResult getInfo(@PathVariable("ID") Long ID)
    {
        return success(rmsTSdaService.selectRmsTSdaByID(ID));
    }

    /**
     * 新增药品说明书
     */
    @PreAuthorize("@ss.hasPermi('rms:sda:add')")
    @Log(title = "药品说明书", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSda rmsTSda)
    {
        return toAjax(rmsTSdaService.insertRmsTSda(rmsTSda));
    }

    /**
     * 修改药品说明书
     */
    @PreAuthorize("@ss.hasPermi('rms:sda:edit')")
    @Log(title = "药品说明书", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSda rmsTSda)
    {
        return toAjax(rmsTSdaService.updateRmsTSda(rmsTSda));
    }

    /**
     * 删除药品说明书
     */
    @PreAuthorize("@ss.hasPermi('rms:sda:remove')")
    @Log(title = "药品说明书", businessType = BusinessType.DELETE)
	@DeleteMapping("/{IDs}")
    public AjaxResult remove(@PathVariable Long[] IDs)
    {
        return toAjax(rmsTSdaService.deleteRmsTSdaByIDs(IDs));
    }
}
