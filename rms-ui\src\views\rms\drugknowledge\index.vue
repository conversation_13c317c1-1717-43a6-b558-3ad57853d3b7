<template>
  <div class="app-container">
    <!-- 药品查询区域 -->
    <div class="search-area">
      <el-card>
        <div slot="header" class="clearfix">
          <span>药品查询</span>
        </div>
        <el-form :model="queryForm" ref="queryForm" size="small" :inline="true" @submit.native.prevent>
          <el-form-item label="检索关键字" prop="keyword">
            <el-input
              v-model="queryForm.keyword"
              placeholder="请输入药品名称、简拼或产品名称"
              clearable
              @keyup.enter.native="handleSearch"
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" @click="handleSearch">搜索</el-button>
            <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 主要内容区域 - 左右分栏 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左侧：药品列表 -->
        <el-col :span="10">
          <el-card>
            <div slot="header" class="clearfix">
              <span>药品列表</span>
            </div>
            <el-table
              v-loading="drugListLoading"
              :data="drugList"
              border
              highlight-current-row
              @row-click="handleRowClick"
              :height="500"
              style="width: 100%"
            >
              <el-table-column width="34" align="center">
                <template slot-scope="scope">
                  <el-radio
                    v-model="selectedDrugIndex"
                    :label="scope.$index"
                  ></el-radio>
                </template>
              </el-table-column>
              <el-table-column label="药品编码" prop="ypCode" width="90" align="center" />
              <el-table-column label="标准库ID" prop="sdaId" width="80" align="center" />
              <el-table-column label="药品名称" prop="drugName" show-overflow-tooltip />
              <el-table-column label="生产厂家" prop="drugManuf" show-overflow-tooltip />
              <el-table-column label="规格" prop="drugSpec" width="100" align="center" />
              <template slot="empty">
                <div class="empty-content">
                  <i class="el-icon-box" style="font-size: 48px; color: #ddd;"></i>
                  <p style="color: #999; margin-top: 0;">暂无药品数据</p>
                  <p style="color: #ccc; margin-top: 0; font-size: 12px;">请输入关键字进行搜索</p>
                </div>
              </template>
            </el-table>
          </el-card>
        </el-col>

        <!-- 右侧：知识库维护 -->
        <el-col :span="14">
          <el-card v-if="selectedDrug" style="min-height: 580px;">
            <div slot="header" class="clearfix">
              <span>{{ selectedDrug.drugName ? '药品名称：' + selectedDrug.drugName : '您还未选择药品' }}</span>
              <span style="color: #999; margin-left: 10px; font-size: 14px;">{{ ' 厂家:' + selectedDrug.drugManuf + ' 规格:' + selectedDrug.drugSpec }}</span>
            </div>

            <!-- Tab标签页 -->
            <el-tabs v-model="activeTab" type="card">
              <el-tab-pane label="儿童用药" name="child">
                <child-drug-knowledge
                  ref="childDrugKnowledge"
                  :drug="selectedDrug"
                  :age-list="ageList"
                />
              </el-tab-pane>
              <el-tab-pane label="常规用量" name="dose">
                <dose-drug-knowledge
                  ref="doseDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
              <el-tab-pane label="药品标识" name="identity">
                <identity-drug-knowledge
                  ref="identityDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
              <el-tab-pane label="给药途径" name="route">
                <route-drug-knowledge
                  ref="routeDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
              <el-tab-pane label="用药性别" name="gender">
                <sex-drug-knowledge
                  ref="sexDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
              <el-tab-pane label="配伍禁忌" name="incompatible">
                <incompatible-drug-knowledge
                  ref="incompatibleDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
              <el-tab-pane label="药物与诊断" name="diagnosis">
                <diagnosis-drug-knowledge
                  ref="diagnosisDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
              <el-tab-pane label="药物禁忌症" name="contraindication">
                <contraindication-drug-knowledge
                  ref="contraindicationDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
              <el-tab-pane label="老年人用药" name="elderly">
                <elder-drug-knowledge
                  ref="elderDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
              <el-tab-pane label="孕妇用药" name="pregnancy">
                <pregnancy-drug-knowledge
                  ref="pregnancyDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
              <el-tab-pane label="过敏" name="allergy">
                <allergy-drug-knowledge
                  ref="allergyDrugKnowledge"
                  :drug="selectedDrug"
                />
              </el-tab-pane>
            </el-tabs>
          </el-card>
          <el-card v-else>
            <div class="empty-content">
              <i class="el-icon-info" style="font-size: 48px; color: #ddd;"></i>
              <p style="color: #999; margin-top: 20px;">请选择药品进行知识库维护</p>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { searchDrugs, getAgeList } from "@/api/rms/drugknowledge"
import ChildDrugKnowledge from "./components/ChildDrugKnowledge"
import ElderDrugKnowledge from "./components/ElderDrugKnowledge"
import PregnancyDrugKnowledge from "./components/PregnancyDrugKnowledge"
import SexDrugKnowledge from "./components/SexDrugKnowledge"
import RouteDrugKnowledge from "./components/RouteDrugKnowledge"
import IdentityDrugKnowledge from "./components/IdentityDrugKnowledge"
import DoseDrugKnowledge from "./components/DoseDrugKnowledge"
import IncompatibleDrugKnowledge from "./components/IncompatibleDrugKnowledge"
import DiagnosisDrugKnowledge from "./components/DiagnosisDrugKnowledge"
import ContraindicationDrugKnowledge from "./components/ContraindicationDrugKnowledge"
import AllergyDrugKnowledge from "./components/AllergyDrugKnowledge"

export default {
  name: "drugknowledge",
  components: {
    ChildDrugKnowledge,
    ElderDrugKnowledge,
    PregnancyDrugKnowledge,
    SexDrugKnowledge,
    RouteDrugKnowledge,
    IdentityDrugKnowledge,
    DoseDrugKnowledge,
    IncompatibleDrugKnowledge,
    DiagnosisDrugKnowledge,
    ContraindicationDrugKnowledge,
    AllergyDrugKnowledge
  },
  data() {
    return {
      // 查询表单
      queryForm: {
        keyword: ''
      },
      // 药品列表
      drugList: [],
      drugListLoading: false,
      // 选中的药品
      selectedDrug: null,
      // 选中的药品索引
      selectedDrugIndex: null,
      // 当前活动的Tab
      activeTab: 'child',
      // 年龄代码列表
      ageList: []
    }
  },
  created() {
    this.loadAgeList()
  },
  watch: {
    // 监听radio选中状态变化
    selectedDrugIndex(newIndex) {
      if (newIndex !== null && this.drugList[newIndex]) {
        this.selectedDrug = this.drugList[newIndex]
        this.activeTab = 'child'
      }
    }
  },
  methods: {
    // 搜索药品
    handleSearch() {
      if (!this.queryForm.keyword) {
        this.$modal.msgError("请输入检索关键字")
        return
      }
      this.drugListLoading = true
      searchDrugs(this.queryForm.keyword).then(response => {
        this.drugList = response.data || []
        this.drugListLoading = false

        // 默认选中第一行
        if (this.drugList.length > 0) {
          this.selectDrug(this.drugList[0], 0)
        } else {
          // 没有数据时重置选中状态
          this.selectedDrug = null
          this.selectedDrugIndex = null
          this.$modal.msgError("未查询到符合条件的药品")
        }
      }).catch(() => {
        this.drugListLoading = false
      })
    },
    // 重置查询
    resetQuery() {
      this.queryForm.keyword = ''
      this.drugList = []
      this.selectedDrug = null
      this.selectedDrugIndex = null
    },
    // 选择药品
    selectDrug(drug, index) {
      this.selectedDrug = drug
      this.selectedDrugIndex = index
      this.activeTab = 'child'
    },
    // 处理行点击
    handleRowClick(row, column, event) {
      // 如果点击的是radio按钮本身，不处理（避免重复触发）
      if (event.target.tagName === 'INPUT' || event.target.closest('.el-radio')) {
        return
      }
      // 直接通过对象引用查找索引，确保索引正确
      const index = this.drugList.indexOf(row)
      if (index !== -1) {
        this.selectDrug(row, index)
      }
    },
    // 加载年龄代码列表
    loadAgeList() {
      getAgeList().then(response => {
        this.ageList = response.data || []
      })
    }
  }
}
</script>

<style scoped>
.app-container {
  margin: 20px;
}

.search-area {
  margin-bottom: 20px;
}

.main-content {
  margin-bottom: 20px;
}

.tab-content {
  padding: 20px;
  text-align: center;
  color: #999;
}

.empty-content {
  text-align: center;
  padding: 60px 0;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>
