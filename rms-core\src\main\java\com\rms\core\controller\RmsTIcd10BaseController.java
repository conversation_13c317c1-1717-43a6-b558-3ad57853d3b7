package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTIcd10Base;
import com.rms.core.service.IRmsTIcd10BaseService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 诊断基础信息表Controller
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/rms/ticd10base")
public class RmsTIcd10BaseController extends BaseController
{
    @Autowired
    private IRmsTIcd10BaseService rmsTIcd10BaseService;

    /**
     * 查询诊断基础信息表列表
     */
    @PreAuthorize("@ss.hasPermi('rms:ticd10base:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTIcd10Base rmsTIcd10Base)
    {
        startPage();
        List<RmsTIcd10Base> list = rmsTIcd10BaseService.selectRmsTIcd10BaseList(rmsTIcd10Base);
        return getDataTable(list);
    }

    /**
     * 导出诊断基础信息表列表
     */
    @PreAuthorize("@ss.hasPermi('rms:ticd10base:export')")
    @Log(title = "诊断基础信息表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTIcd10Base rmsTIcd10Base)
    {
        List<RmsTIcd10Base> list = rmsTIcd10BaseService.selectRmsTIcd10BaseList(rmsTIcd10Base);
        ExcelUtil<RmsTIcd10Base> util = new ExcelUtil<RmsTIcd10Base>(RmsTIcd10Base.class);
        util.exportExcel(response, list, "诊断基础信息表数据");
    }

    /**
     * 获取诊断基础信息表详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:ticd10base:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTIcd10BaseService.selectRmsTIcd10BaseById(id));
    }

    /**
     * 新增诊断基础信息表
     */
    @PreAuthorize("@ss.hasPermi('rms:ticd10base:add')")
    @Log(title = "诊断基础信息表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTIcd10Base rmsTIcd10Base)
    {
        return toAjax(rmsTIcd10BaseService.insertRmsTIcd10Base(rmsTIcd10Base));
    }

    /**
     * 修改诊断基础信息表
     */
    @PreAuthorize("@ss.hasPermi('rms:ticd10base:edit')")
    @Log(title = "诊断基础信息表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTIcd10Base rmsTIcd10Base)
    {
        return toAjax(rmsTIcd10BaseService.updateRmsTIcd10Base(rmsTIcd10Base));
    }

    /**
     * 删除诊断基础信息表
     */
    @PreAuthorize("@ss.hasPermi('rms:ticd10base:remove')")
    @Log(title = "诊断基础信息表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTIcd10BaseService.deleteRmsTIcd10BaseByIds(ids));
    }
}
