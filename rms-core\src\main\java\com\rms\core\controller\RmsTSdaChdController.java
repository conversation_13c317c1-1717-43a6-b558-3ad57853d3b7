package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaChd;
import com.rms.core.service.IRmsTSdaChdService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 儿童用药规则库Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/rms/tsdachd")
public class RmsTSdaChdController extends BaseController
{
    @Autowired
    private IRmsTSdaChdService rmsTSdaChdService;

    /**
     * 查询儿童用药规则库列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdachd:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaChd rmsTSdaChd)
    {
        startPage();
        List<RmsTSdaChd> list = rmsTSdaChdService.selectRmsTSdaChdList(rmsTSdaChd);
        return getDataTable(list);
    }

    /**
     * 导出儿童用药规则库列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdachd:export')")
    @Log(title = "儿童用药规则库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaChd rmsTSdaChd)
    {
        List<RmsTSdaChd> list = rmsTSdaChdService.selectRmsTSdaChdList(rmsTSdaChd);
        ExcelUtil<RmsTSdaChd> util = new ExcelUtil<RmsTSdaChd>(RmsTSdaChd.class);
        util.exportExcel(response, list, "儿童用药规则库数据");
    }

    /**
     * 获取儿童用药规则库详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdachd:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTSdaChdService.selectRmsTSdaChdById(id));
    }

    /**
     * 新增儿童用药规则库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdachd:add')")
    @Log(title = "儿童用药规则库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaChd rmsTSdaChd)
    {
        return toAjax(rmsTSdaChdService.insertRmsTSdaChd(rmsTSdaChd));
    }

    /**
     * 修改儿童用药规则库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdachd:edit')")
    @Log(title = "儿童用药规则库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaChd rmsTSdaChd)
    {
        return toAjax(rmsTSdaChdService.updateRmsTSdaChd(rmsTSdaChd));
    }

    /**
     * 删除儿童用药规则库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdachd:remove')")
    @Log(title = "儿童用药规则库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTSdaChdService.deleteRmsTSdaChdByIds(ids));
    }
}
