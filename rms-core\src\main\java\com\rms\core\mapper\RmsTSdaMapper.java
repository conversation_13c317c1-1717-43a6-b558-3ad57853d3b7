package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTSda;

/**
 * 药品说明书Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-06
 */
public interface RmsTSdaMapper 
{
    /**
     * 查询药品说明书
     * 
     * @param ID 药品说明书主键
     * @return 药品说明书
     */
    public RmsTSda selectRmsTSdaByID(Long ID);

    /**
     * 查询药品说明书列表
     * 
     * @param rmsTSda 药品说明书
     * @return 药品说明书集合
     */
    public List<RmsTSda> selectRmsTSdaList(RmsTSda rmsTSda);

    /**
     * 新增药品说明书
     * 
     * @param rmsTSda 药品说明书
     * @return 结果
     */
    public int insertRmsTSda(RmsTSda rmsTSda);

    /**
     * 修改药品说明书
     * 
     * @param rmsTSda 药品说明书
     * @return 结果
     */
    public int updateRmsTSda(RmsTSda rmsTSda);

    /**
     * 删除药品说明书
     * 
     * @param ID 药品说明书主键
     * @return 结果
     */
    public int deleteRmsTSdaByID(Long ID);

    /**
     * 批量删除药品说明书
     * 
     * @param IDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaByIDs(Long[] IDs);
}
