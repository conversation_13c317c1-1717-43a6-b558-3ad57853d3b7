package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTIcd10Base;

/**
 * 诊断基础信息表Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface RmsTIcd10BaseMapper 
{
    /**
     * 查询诊断基础信息表
     * 
     * @param id 诊断基础信息表主键
     * @return 诊断基础信息表
     */
    public RmsTIcd10Base selectRmsTIcd10BaseById(Long id);

    /**
     * 查询诊断基础信息表列表
     * 
     * @param rmsTIcd10Base 诊断基础信息表
     * @return 诊断基础信息表集合
     */
    public List<RmsTIcd10Base> selectRmsTIcd10BaseList(RmsTIcd10Base rmsTIcd10Base);

    /**
     * 新增诊断基础信息表
     * 
     * @param rmsTIcd10Base 诊断基础信息表
     * @return 结果
     */
    public int insertRmsTIcd10Base(RmsTIcd10Base rmsTIcd10Base);

    /**
     * 修改诊断基础信息表
     * 
     * @param rmsTIcd10Base 诊断基础信息表
     * @return 结果
     */
    public int updateRmsTIcd10Base(RmsTIcd10Base rmsTIcd10Base);

    /**
     * 删除诊断基础信息表
     * 
     * @param id 诊断基础信息表主键
     * @return 结果
     */
    public int deleteRmsTIcd10BaseById(Long id);

    /**
     * 批量删除诊断基础信息表
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTIcd10BaseByIds(Long[] ids);

    /**
     * 根据关键字搜索ICD诊断信息
     * 
     * @param keyword 搜索关键字
     * @return ICD诊断信息集合
     */
    public List<RmsTIcd10Base> searchByKeyword(String keyword);
}
