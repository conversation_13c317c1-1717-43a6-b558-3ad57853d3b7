package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTSdaSex;

/**
 * 标准药品性别库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface RmsTSdaSexMapper 
{
    /**
     * 查询标准药品性别库
     * 
     * @param id 标准药品性别库主键
     * @return 标准药品性别库
     */
    public RmsTSdaSex selectRmsTSdaSexById(Long id);

    /**
     * 查询标准药品性别库列表
     * 
     * @param rmsTSdaSex 标准药品性别库
     * @return 标准药品性别库集合
     */
    public List<RmsTSdaSex> selectRmsTSdaSexList(RmsTSdaSex rmsTSdaSex);

    /**
     * 新增标准药品性别库
     * 
     * @param rmsTSdaSex 标准药品性别库
     * @return 结果
     */
    public int insertRmsTSdaSex(RmsTSdaSex rmsTSdaSex);

    /**
     * 修改标准药品性别库
     * 
     * @param rmsTSdaSex 标准药品性别库
     * @return 结果
     */
    public int updateRmsTSdaSex(RmsTSdaSex rmsTSdaSex);

    /**
     * 删除标准药品性别库
     * 
     * @param id 标准药品性别库主键
     * @return 结果
     */
    public int deleteRmsTSdaSexById(Long id);

    /**
     * 批量删除标准药品性别库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaSexByIds(Long[] ids);
}
