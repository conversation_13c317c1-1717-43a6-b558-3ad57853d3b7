package com.rms.core.domain.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 基本信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class BaseInfoDTO {

    /**
     * 调用方标识（固定值：HIS）
     */
    @NotBlank(message = "调用方标识不能为空")
    private String source;

    /**
     * 医院编码，即医疗机构代码
     */
    @NotBlank(message = "医院编码不能为空")
    private String hospCode;

    /**
     * 科室代码
     */
    @NotBlank(message = "科室代码不能为空")
    private String deptCode;

    /**
     * 科室名称
     */
    @NotBlank(message = "科室名称不能为空")
    private String deptName;

    /**
     * 医生信息
     */
    @Valid
    @NotNull(message = "医生信息不能为空")
    private DoctorDTO doctor;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getHospCode() {
        return hospCode;
    }

    public void setHospCode(String hospCode) {
        this.hospCode = hospCode;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public DoctorDTO getDoctor() {
        return doctor;
    }

    public void setDoctor(DoctorDTO doctor) {
        this.doctor = doctor;
    }

    @Override
    public String toString() {
        return "BaseInfoDTO{" +
                "source='" + source + '\'' +
                ", hospCode='" + hospCode + '\'' +
                ", deptCode='" + deptCode + '\'' +
                ", deptName='" + deptName + '\'' +
                ", doctor=" + doctor +
                '}';
    }
}
