package com.rms.core.exception;

/**
 * RMS API业务异常类
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class RmsApiException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    /**
     * 错误码
     */
    private Integer code;

    /**
     * 错误消息
     */
    private String message;

    public RmsApiException() {
        super();
    }

    public RmsApiException(String message) {
        super(message);
        this.message = message;
    }

    public RmsApiException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public RmsApiException(String message, Throwable cause) {
        super(message, cause);
        this.message = message;
    }

    public RmsApiException(Integer code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    @Override
    public String toString() {
        return "RmsApiException{" +
                "code=" + code +
                ", message='" + message + '\'' +
                '}';
    }
}
