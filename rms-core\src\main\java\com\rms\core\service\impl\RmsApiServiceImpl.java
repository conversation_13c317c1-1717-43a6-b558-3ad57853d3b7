package com.rms.core.service.impl;

import com.rms.core.domain.RmsTPresGm;
import com.rms.core.domain.dto.*;
import com.rms.core.exception.RmsApiException;
import com.rms.core.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.rms.core.domain.RmsTPres;
import com.rms.core.domain.RmsTPresMed;
import com.rms.core.domain.RmsTPresFx;
import com.rms.core.mapper.RmsApiMapper;
import com.rms.common.utils.DateUtils;
import com.rms.common.utils.uuid.IdUtils;
import org.springframework.transaction.annotation.Transactional;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;
import java.util.Map;
import java.util.HashMap;
import java.time.LocalDate;
import java.time.Period;


/**
 * 合理用药与事前审方系统API Service实现类
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Service
public class RmsApiServiceImpl implements IRmsApiService {

    @Autowired
    private IRmsTPresService rmsTPresService;

    @Autowired
    private IRmsTPresMedService rmsTPresMedService;

    @Autowired
    private IRmsTPresFxService rmsTPresFxService;

    @Autowired
    private RmsApiMapper rmsApiMapper;

    @Autowired
    private IRmsTPresGmService rmsTPresGmService;

    private static final Logger logger = LoggerFactory.getLogger(RmsApiServiceImpl.class);

    /**
     * 处方分析
     *
     * @param request 处方分析请求
     * @return 处方分析结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public PrescriptionAnalyzeResponseDTO analyzePrescription(PrescriptionAnalyzeRequestDTO request) {
        if (request == null || request.getBaseInfo() == null || request.getDetails() == null) {
            throw new RmsApiException("请求参数不能为空");
        }
        logger.info("开始处方分析并落库，医院编码：{}，就诊号：{}", request.getBaseInfo().getHospCode(), request.getDetails().getTreatCode());

        try {
            // 1) 准备通用信息
            BaseInfoDTO base = request.getBaseInfo();
            PrescriptionAnalyzeDetailsDTO details = request.getDetails();
            PatientDTO patient = details.getPatient();

            // 2) 组装过敏与诊断信息（文本存库）
            String allInfo = buildAllergicInfo(patient);
            String diaInfo = buildDiagnosisInfo(patient, details);

            // 3) 遍历处方，落库主表与明细表
            List<String> prescriptionCodes = new ArrayList<>(); // 保存所有处方编码，用于后续分析
            for (PrescriptionDataDTO presDTO : details.getPrescriptionData()) {
                // 3.1 创建处方主表记录
                RmsTPres pres = new RmsTPres();
                // 处方编码（系统内部唯一主键）
                String code = IdUtils.fastSimpleUUID();
                prescriptionCodes.add(code); // 添加到列表中
                pres.setCode(code);

                // 基本信息
                pres.setHospCode(base.getHospCode());
                pres.setDeptCode(base.getDeptCode());
                pres.setDeptName(base.getDeptName());
                if (base.getDoctor() != null) {
                    pres.setDoctCode(base.getDoctor().getCode());
                    pres.setDoctName(base.getDoctor().getName());
                    pres.setDoctType(base.getDoctor().getType());
                    pres.setDoctTypeName(base.getDoctor().getTypeName());
                }

                // 就诊信息
                pres.setHisTime(DateUtils.parseDate(details.getHisTime()));
                pres.setHospFlag(details.getHospFlag());
                pres.setTreatType(details.getTreatType() == null ? null : String.valueOf(details.getTreatType()));
                pres.setTreatCode(details.getTreatCode());
                pres.setBedNo(details.getBedNo());

                // 患者信息
                if (patient != null) {
                    pres.setName(patient.getName());
                    pres.setBirth(patient.getBirth());
                    pres.setSex(patient.getSex());
                    pres.setWeight(patient.getWeight() == null ? null : String.valueOf(patient.getWeight()));
                    pres.setHeight(patient.getHeight() == null ? null : String.valueOf(patient.getHeight()));
                    pres.setIdCard(patient.getIdCard());
                    pres.setCardType(patient.getCardType() == null ? null : String.valueOf(patient.getCardType()));
                    pres.setCardCode(patient.getCardCode());
                    pres.setPregnantUnit(patient.getPregnantUnit());
                    pres.setPregnant(patient.getPregnant() == null ? null : String.valueOf(patient.getPregnant()));
                }
                pres.setAllInfo(allInfo);
                pres.setDiaInfo(diaInfo);

                // 处方自身信息
                pres.setPresId(presDTO.getId());
                pres.setReason(presDTO.getReason());
                pres.setIsUrgent(presDTO.getIsUrgent() == null ? null : String.valueOf(presDTO.getIsUrgent()));
                pres.setIsNew(presDTO.getIsNew() == null ? null : String.valueOf(presDTO.getIsNew()));
                pres.setIsCurrent(presDTO.getIsCurrent() == null ? null : String.valueOf(presDTO.getIsCurrent()));
                pres.setPresType(presDTO.getPresType());
                pres.setPresTime(DateUtils.parseDate(presDTO.getPresTime()));
                pres.setDischargeDrug(presDTO.getDischargeDrug() == null ? null : String.valueOf(presDTO.getDischargeDrug()));
                pres.setPrescriptionType(presDTO.getPrescriptionType() == null ? null : String.valueOf(presDTO.getPrescriptionType()));
                pres.setPresSm(presDTO.getPresSm());
                pres.setAdmType(presDTO.getAdmType() == null ? null : String.valueOf(presDTO.getAdmType()));
                pres.setRequir(presDTO.getRequir());
                pres.setCs1(presDTO.getCs1() == null ? null : String.valueOf(presDTO.getCs1()));
                pres.setTs(presDTO.getTs() == null ? null : String.valueOf(presDTO.getTs()));
                pres.setSolvent(presDTO.getSolvent());
                pres.setJl(presDTO.getJl());
                pres.setCs2(presDTO.getCs2() == null ? null : String.valueOf(presDTO.getCs2()));
                pres.setLb(presDTO.getLb());
                pres.setFs1(presDTO.getFs1());
                pres.setFs2(presDTO.getFs2());
                // 默认状态：0 无问题
                pres.setFlag(0L);

                // 落库主表
                rmsTPresService.insertRmsTPres(pres);

                // 3.2 创建处方药品明细记录
                if (presDTO.getMedicineData() != null) {
                    for (MedicineDataDTO medDTO : presDTO.getMedicineData()) {
                        RmsTPresMed med = new RmsTPresMed();
                        med.setCode(code);              // 关联同一处方编码
                        med.setPresId(presDTO.getId()); // 关联处方号
                        med.setMedName(medDTO.getName());
                        med.setHisCode(medDTO.getHisCode());
                        med.setInsurCode(medDTO.getInsurCode());
                        med.setApproval(medDTO.getApproval());
                        med.setSpec(medDTO.getSpec());
                        med.setGroup(medDTO.getGroup());
                        med.setReason(medDTO.getReason());
                        med.setDoseUnit(medDTO.getDoseUnit());
                        med.setDose(medDTO.getDose() == null ? null : String.valueOf(medDTO.getDose()));
                        med.setOrdQty(medDTO.getOrdQty() == null ? null : String.valueOf(medDTO.getOrdQty()));
                        med.setOrdUom(medDTO.getOrdUom());
                        med.setFreq(medDTO.getFreq());
                        med.setAdminister(medDTO.getAdminister());
                        med.setBeginTime(medDTO.getBeginTime());
                        med.setEndTime(medDTO.getEndTime());
                        med.setDays(medDTO.getDays() == null ? null : String.valueOf(medDTO.getDays()));
                        med.setMoney(medDTO.getMoney() == null ? null : String.valueOf(medDTO.getMoney()));
                        med.setYysm(medDTO.getYysm());
                        med.setBz(medDTO.getBz());
                        // 可选：把用药理由同步到medReason1
                        med.setMedReason1(medDTO.getReason());

                        rmsTPresMedService.insertRmsTPresMed(med);
                    }
                }

                // 3.3 创建处方过敏源记录
                if (patient.getAllergicData() != null) {
                    for (AllergicDataDTO allergy : patient.getAllergicData()) {
                        RmsTPresGm gm = new RmsTPresGm();
                        gm.setCode(code);
                        gm.setType(allergy.getType());
                        gm.setName(allergy.getName());
                        gm.setGmdm(allergy.getCode());
                        rmsTPresGmService.insertRmsTPresGm(gm);
                    }
                }
            }

            // 4) 调用合理用药分析存储过程并处理分析结果
            PrescriptionAnalyzeResponseDTO response = new PrescriptionAnalyzeResponseDTO();
            response.setProblemLevel(0);
            response.setProblemDetail("");
            response.setDoubleSignFlag(0);
            response.setDoubleSignDrugs("");

            // 对每个处方调用存储过程进行分析
            for (String code : prescriptionCodes) {
                try {
                    // 4.1 调用合理用药分析存储过程
                    logger.info("开始调用合理用药分析存储过程，处方编码：{}", code);

                    // 获取医保编码（从患者卡号中获取，如果卡类型为医保卡）
                    String akb020 = null;
                    if (patient != null && patient.getCardType() != null && patient.getCardType() == 1) {
                        akb020 = patient.getCardCode(); // 医保卡号作为医保编码
                    }

                    // 准备存储过程参数
                    Map<String, Object> params = new HashMap<>();
                    params.put("pCode", code);
                    params.put("pAkb020", akb020);
                    // 添加OUT参数占位符
                    params.put("pResultCode", null);
                    params.put("pResultMessage", null);

                    // 调用存储过程
                    int result = rmsApiMapper.callRmsFxMain(params);

                    // 获取存储过程的输出参数
                    Integer resultCode = (Integer) params.get("pResultCode");
                    String resultMessage = (String) params.get("pResultMessage");

                    logger.info("存储过程执行完成，处方编码：{}，执行结果：{}，返回代码：{}，返回消息：{}",
                               code, result, resultCode, resultMessage);

                    // 4.3 查询分析结果
                    RmsTPresFx queryCondition = new RmsTPresFx();
                    queryCondition.setCode(code);
                    List<RmsTPresFx> analysisResults = rmsTPresFxService.selectRmsTPresFxList(queryCondition);

                    logger.info("查询到处方分析结果，处方编码：{}，结果数量：{}", code, analysisResults.size());

                    // 4.4 处理分析结果
                    if (analysisResults != null && !analysisResults.isEmpty()) {
                        // 存在用药问题，需要处理
                        logger.info("发现用药问题，处方编码：{}，问题数量：{}", code, analysisResults.size());

                        // 计算最大问题等级代码
                        int maxProblemLevel = 0;
                        StringBuilder problemDetails = new StringBuilder();

                        for (RmsTPresFx fxResult : analysisResults) {
                            // 获取问题等级代码
                            if (fxResult.getWtlvlcode() != null) {
                                try {
                                    int levelCode = Integer.parseInt(fxResult.getWtlvlcode());
                                    if (levelCode > maxProblemLevel) {
                                        maxProblemLevel = levelCode;
                                    }
                                } catch (NumberFormatException e) {
                                    logger.warn("问题等级代码格式错误：{}", fxResult.getWtlvlcode());
                                }
                            }

                            // 拼接问题详情
                            if (fxResult.getDetail() != null && !fxResult.getDetail().trim().isEmpty()) {
                                if (problemDetails.length() > 0) {
                                    problemDetails.append("；");
                                }
                                problemDetails.append(fxResult.getDetail());
                            }
                        }

                        // 更新响应中的问题等级和详情（取所有处方中的最大值）
                        if (maxProblemLevel > response.getProblemLevel()) {
                            response.setProblemLevel(maxProblemLevel);
                        }

                        if (problemDetails.length() > 0) {
                            if (response.getProblemDetail() != null && !response.getProblemDetail().isEmpty()) {
                                response.setProblemDetail(response.getProblemDetail() + "；" + problemDetails.toString());
                            } else {
                                response.setProblemDetail(problemDetails.toString());
                            }
                        }

                        logger.info("处方问题处理完成，处方编码：{}，当前最大问题等级：{}", code, response.getProblemLevel());
                    } else {
                        logger.info("处方无问题，处方编码：{}", code);
                    }

                } catch (Exception e) {
                    logger.error("处方分析过程中发生异常，处方编码：{}", code, e);
                    // 单个处方分析失败不影响整体流程，继续处理下一个处方
                }
            }

            logger.info("处方分析与落库完成：就诊号={}，处方数={}，最终问题等级={}",
                    details.getTreatCode(),
                    details.getPrescriptionData() == null ? 0 : details.getPrescriptionData().size(),
                    response.getProblemLevel());
            return response;
        } catch (Exception e) {
            logger.error("处方分析或落库失败", e);
            throw new RmsApiException("处方分析或保存失败：" + e.getMessage(), e);
        }
    }

    /**
     * 拼接过敏信息文本
     */
    private String buildAllergicInfo(PatientDTO patient) {
        if (patient == null || patient.getAllergicData() == null || patient.getAllergicData().isEmpty()) {
            return null;
        }
        StringJoiner joiner = new StringJoiner("；");
        for (AllergicDataDTO a : patient.getAllergicData()) {
            if (a == null) continue;
            String type = a.getType() == null ? "" : String.valueOf(a.getType());
            joiner.add(a.getName() + "(" + a.getCode() + ")-" + type);
        }
        return joiner.toString();
    }

    /**
     * 拼接诊断信息文本（含患者诊断与处方诊断）
     */
    private String buildDiagnosisInfo(PatientDTO patient, PrescriptionAnalyzeDetailsDTO details) {
        List<DiagnoseDataDTO> list = new ArrayList<>();
        if (patient != null && patient.getDiagnoseData() != null) {
            list.addAll(patient.getDiagnoseData());
        }
        if (details != null && details.getPrescriptionData() != null) {
            for (PrescriptionDataDTO p : details.getPrescriptionData()) {
                if (p.getPresDiagnoseData() != null) {
                    list.addAll(p.getPresDiagnoseData());
                }
            }
        }
        if (list.isEmpty()) {
            return null;
        }
        StringJoiner joiner = new StringJoiner("；");
        for (DiagnoseDataDTO d : list) {
            if (d == null) continue;
            String type = d.getType() == null ? "" : String.valueOf(d.getType());
            joiner.add(d.getName() + "(" + d.getCode() + ")-" + type);
        }
        return joiner.toString();
    }

    /**
     * 获取处方审核结果
     *
     * @param request 获取处方审核结果请求
     * @return 处方审核结果
     */
    @Override
    public PrescriptionCheckResultResponseDTO getPrescriptionCheckResult(PrescriptionCheckResultRequestDTO request) {
        logger.info("查询处方审核结果，就诊号：{}，处方号：{}",
                   request.getDetails().getTreatCode(),
                   request.getDetails().getPrescriptionId());

        try {
            PrescriptionCheckResultResponseDTO response = new PrescriptionCheckResultResponseDTO();

            // TODO: 实现具体的审核结果查询逻辑
            // 1. 根据就诊号和处方号查询审核状态
            // 2. 返回审核状态：0-无需人工干预或审核通过；1-待审核；2-审核不通过

            // 示例逻辑：默认返回审核通过
            response.setCheckStatus(0);

            logger.info("处方审核结果查询完成，审核状态：{}", response.getCheckStatus());
            return response;

        } catch (Exception e) {
            logger.error("查询处方审核结果失败：{}", e.getMessage(), e);
            throw new RuntimeException("查询处方审核结果失败：" + e.getMessage());
        }
    }

    /**
     * 获取药品信息
     *
     * @param request 药品信息查询请求
     * @return 药品信息
     */
    @Override
    public MedicineInfoResponseDTO getMedicineInfo(MedicineInfoRequestDTO request) {
        logger.info("查询药品信息，药品代码：{}", request.getDetails().getMedicine().getHisCode());

        try {
            MedicineInfoResponseDTO response = new MedicineInfoResponseDTO();
            MedicineInfoResponseDTO.MedicineInfoDataDTO info = new MedicineInfoResponseDTO.MedicineInfoDataDTO();

            // TODO: 实现具体的药品信息查询逻辑
            // 1. 根据医院药品代码查询药品信息
            // 2. 返回药品名称、拼音缩写等信息

            // 示例逻辑：返回基本信息
            String hisCode = request.getDetails().getMedicine().getHisCode();
            String hisName = request.getDetails().getMedicine().getHisName();

            info.setYm(hisName != null ? hisName : "未知药品");

            response.setInfo(info);

            logger.info("药品信息查询完成，药品名称：{}", info.getYm());
            return response;

        } catch (Exception e) {
            logger.error("查询药品信息失败：{}", e.getMessage(), e);
            throw new RuntimeException("查询药品信息失败：" + e.getMessage());
        }
    }

}
