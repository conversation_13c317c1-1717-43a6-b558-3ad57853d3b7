<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rms.core.mapper.RmsApiMapper">
    
    <!-- 调用合理用药分析存储过程 -->
    <update id="callRmsFxMain" parameterType="map" statementType="CALLABLE">
        {CALL rms_fx_main(
            #{pCode, mode=IN, jdbcType=VARCHAR},
            #{pAkb020, mode=IN, jdbcType=VARCHAR},
            #{pResultCode, mode=OUT, jdbcType=INTEGER},
            #{pResultMessage, mode=OUT, jdbcType=VARCHAR}
        )}
    </update>
    
</mapper>
