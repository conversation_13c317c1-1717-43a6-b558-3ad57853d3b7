package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaIcd10Info;
import com.rms.core.service.IRmsTSdaIcd10InfoService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 药物与诊断信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/rms/tsdaicd10info")
public class RmsTSdaIcd10InfoController extends BaseController
{
    @Autowired
    private IRmsTSdaIcd10InfoService rmsTSdaIcd10InfoService;

    /**
     * 查询药物与诊断信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaicd10info:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        startPage();
        List<RmsTSdaIcd10Info> list = rmsTSdaIcd10InfoService.selectRmsTSdaIcd10InfoList(rmsTSdaIcd10Info);
        return getDataTable(list);
    }

    /**
     * 导出药物与诊断信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaicd10info:export')")
    @Log(title = "药物与诊断信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        List<RmsTSdaIcd10Info> list = rmsTSdaIcd10InfoService.selectRmsTSdaIcd10InfoList(rmsTSdaIcd10Info);
        ExcelUtil<RmsTSdaIcd10Info> util = new ExcelUtil<RmsTSdaIcd10Info>(RmsTSdaIcd10Info.class);
        util.exportExcel(response, list, "药物与诊断信息数据");
    }

    /**
     * 获取药物与诊断信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaicd10info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTSdaIcd10InfoService.selectRmsTSdaIcd10InfoById(id));
    }

    /**
     * 新增药物与诊断信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaicd10info:add')")
    @Log(title = "药物与诊断信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        return toAjax(rmsTSdaIcd10InfoService.insertRmsTSdaIcd10Info(rmsTSdaIcd10Info));
    }

    /**
     * 修改药物与诊断信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaicd10info:edit')")
    @Log(title = "药物与诊断信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaIcd10Info rmsTSdaIcd10Info)
    {
        return toAjax(rmsTSdaIcd10InfoService.updateRmsTSdaIcd10Info(rmsTSdaIcd10Info));
    }

    /**
     * 删除药物与诊断信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdaicd10info:remove')")
    @Log(title = "药物与诊断信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTSdaIcd10InfoService.deleteRmsTSdaIcd10InfoByIds(ids));
    }
}
