package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTXhzyEdiZy;

/**
 * 中药相互作用Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface RmsTXhzyEdiZyMapper 
{
    /**
     * 查询中药相互作用
     * 
     * @param id 中药相互作用主键
     * @return 中药相互作用
     */
    public RmsTXhzyEdiZy selectRmsTXhzyEdiZyById(Long id);

    /**
     * 查询中药相互作用列表
     * 
     * @param rmsTXhzyEdiZy 中药相互作用
     * @return 中药相互作用集合
     */
    public List<RmsTXhzyEdiZy> selectRmsTXhzyEdiZyList(RmsTXhzyEdiZy rmsTXhzyEdiZy);

    /**
     * 新增中药相互作用
     * 
     * @param rmsTXhzyEdiZy 中药相互作用
     * @return 结果
     */
    public int insertRmsTXhzyEdiZy(RmsTXhzyEdiZy rmsTXhzyEdiZy);

    /**
     * 修改中药相互作用
     * 
     * @param rmsTXhzyEdiZy 中药相互作用
     * @return 结果
     */
    public int updateRmsTXhzyEdiZy(RmsTXhzyEdiZy rmsTXhzyEdiZy);

    /**
     * 删除中药相互作用
     * 
     * @param id 中药相互作用主键
     * @return 结果
     */
    public int deleteRmsTXhzyEdiZyById(Long id);

    /**
     * 批量删除中药相互作用
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTXhzyEdiZyByIds(Long[] ids);

    /**
     * 根据sdaId查询配伍禁忌信息（包含药物名称）
     * 
     * @param sdaId 标准数据ID
     * @return 配伍禁忌信息集合
     */
    public List<com.rms.core.domain.IncompatibleDrugResult> selectIncompatibleRulesZyBySdaId(Long sdaId);
}
