package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTPresFx;

/**
 * 处方分析结果Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-18
 */
public interface RmsTPresFxMapper 
{
    /**
     * 查询处方分析结果
     * 
     * @param id 处方分析结果主键
     * @return 处方分析结果
     */
    public RmsTPresFx selectRmsTPresFxById(Long id);

    /**
     * 查询处方分析结果列表
     * 
     * @param rmsTPresFx 处方分析结果
     * @return 处方分析结果集合
     */
    public List<RmsTPresFx> selectRmsTPresFxList(RmsTPresFx rmsTPresFx);

    /**
     * 新增处方分析结果
     * 
     * @param rmsTPresFx 处方分析结果
     * @return 结果
     */
    public int insertRmsTPresFx(RmsTPresFx rmsTPresFx);

    /**
     * 修改处方分析结果
     * 
     * @param rmsTPresFx 处方分析结果
     * @return 结果
     */
    public int updateRmsTPresFx(RmsTPresFx rmsTPresFx);

    /**
     * 删除处方分析结果
     * 
     * @param id 处方分析结果主键
     * @return 结果
     */
    public int deleteRmsTPresFxById(Long id);

    /**
     * 批量删除处方分析结果
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTPresFxByIds(Long[] ids);
}
