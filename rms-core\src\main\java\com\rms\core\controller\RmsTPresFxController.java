package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTPresFx;
import com.rms.core.service.IRmsTPresFxService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 处方分析结果Controller
 *
 * <AUTHOR>
 * @date 2025-07-18
 */
@RestController
@RequestMapping("/rms/tpresfx")
public class RmsTPresFxController extends BaseController
{
    @Autowired
    private IRmsTPresFxService rmsTPresFxService;

    /**
     * 查询处方分析结果列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresfx:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTPresFx rmsTPresFx)
    {
        startPage();
        List<RmsTPresFx> list = rmsTPresFxService.selectRmsTPresFxList(rmsTPresFx);
        return getDataTable(list);
    }

    /**
     * 导出处方分析结果列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresfx:export')")
    @Log(title = "处方分析结果", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTPresFx rmsTPresFx)
    {
        List<RmsTPresFx> list = rmsTPresFxService.selectRmsTPresFxList(rmsTPresFx);
        ExcelUtil<RmsTPresFx> util = new ExcelUtil<RmsTPresFx>(RmsTPresFx.class);
        util.exportExcel(response, list, "处方分析结果数据");
    }

    /**
     * 获取处方分析结果详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresfx:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTPresFxService.selectRmsTPresFxById(id));
    }

    /**
     * 新增处方分析结果
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresfx:add')")
    @Log(title = "处方分析结果", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTPresFx rmsTPresFx)
    {
        return toAjax(rmsTPresFxService.insertRmsTPresFx(rmsTPresFx));
    }

    /**
     * 修改处方分析结果
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresfx:edit')")
    @Log(title = "处方分析结果", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTPresFx rmsTPresFx)
    {
        return toAjax(rmsTPresFxService.updateRmsTPresFx(rmsTPresFx));
    }

    /**
     * 删除处方分析结果
     */
    @PreAuthorize("@ss.hasPermi('rms:tpresfx:remove')")
    @Log(title = "处方分析结果", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTPresFxService.deleteRmsTPresFxByIds(ids));
    }
}
