package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTPresSh;
import com.rms.core.service.IRmsTPresShService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 处方审核意见Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/rms/tpressh")
public class RmsTPresShController extends BaseController
{
    @Autowired
    private IRmsTPresShService rmsTPresShService;

    /**
     * 查询处方审核意见列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpressh:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTPresSh rmsTPresSh)
    {
        startPage();
        List<RmsTPresSh> list = rmsTPresShService.selectRmsTPresShList(rmsTPresSh);
        return getDataTable(list);
    }

    /**
     * 导出处方审核意见列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tpressh:export')")
    @Log(title = "处方审核意见", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTPresSh rmsTPresSh)
    {
        List<RmsTPresSh> list = rmsTPresShService.selectRmsTPresShList(rmsTPresSh);
        ExcelUtil<RmsTPresSh> util = new ExcelUtil<RmsTPresSh>(RmsTPresSh.class);
        util.exportExcel(response, list, "处方审核意见数据");
    }

    /**
     * 获取处方审核意见详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tpressh:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTPresShService.selectRmsTPresShById(id));
    }

    /**
     * 新增处方审核意见
     */
    @PreAuthorize("@ss.hasPermi('rms:tpressh:add')")
    @Log(title = "处方审核意见", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTPresSh rmsTPresSh)
    {
        return toAjax(rmsTPresShService.insertRmsTPresSh(rmsTPresSh));
    }

    /**
     * 修改处方审核意见
     */
    @PreAuthorize("@ss.hasPermi('rms:tpressh:edit')")
    @Log(title = "处方审核意见", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTPresSh rmsTPresSh)
    {
        return toAjax(rmsTPresShService.updateRmsTPresSh(rmsTPresSh));
    }

    /**
     * 删除处方审核意见
     */
    @PreAuthorize("@ss.hasPermi('rms:tpressh:remove')")
    @Log(title = "处方审核意见", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTPresShService.deleteRmsTPresShByIds(ids));
    }
}
