package com.rms.core.domain.dto;

/**
 * 处方分析响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class PrescriptionAnalyzeResponseDTO {

    /**
     * 问题级别：0-无问题；1-其他问题；2-一般问题；3-严重问题
     */
    private Integer problemLevel;

    /**
     * 问题详情（问题级别>0时有值）
     */
    private String problemDetail;

    /**
     * 双签标志：0-不需要；1-需要
     */
    private Integer doubleSignFlag;

    /**
     * 需双签药品代码（分号分隔，doubleSignFlag=1时有效）
     */
    private String doubleSignDrugs;

    public Integer getProblemLevel() {
        return problemLevel;
    }

    public void setProblemLevel(Integer problemLevel) {
        this.problemLevel = problemLevel;
    }

    public String getProblemDetail() {
        return problemDetail;
    }

    public void setProblemDetail(String problemDetail) {
        this.problemDetail = problemDetail;
    }

    public Integer getDoubleSignFlag() {
        return doubleSignFlag;
    }

    public void setDoubleSignFlag(Integer doubleSignFlag) {
        this.doubleSignFlag = doubleSignFlag;
    }

    public String getDoubleSignDrugs() {
        return doubleSignDrugs;
    }

    public void setDoubleSignDrugs(String doubleSignDrugs) {
        this.doubleSignDrugs = doubleSignDrugs;
    }

    @Override
    public String toString() {
        return "PrescriptionAnalyzeResponseDTO{" +
                "problemLevel=" + problemLevel +
                ", problemDetail='" + problemDetail + '\'' +
                ", doubleSignFlag=" + doubleSignFlag +
                ", doubleSignDrugs='" + doubleSignDrugs + '\'' +
                '}';
    }
}
