package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTPresSh;

/**
 * 处方审核意见Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface RmsTPresShMapper 
{
    /**
     * 查询处方审核意见
     * 
     * @param id 处方审核意见主键
     * @return 处方审核意见
     */
    public RmsTPresSh selectRmsTPresShById(Long id);

    /**
     * 查询处方审核意见列表
     * 
     * @param rmsTPresSh 处方审核意见
     * @return 处方审核意见集合
     */
    public List<RmsTPresSh> selectRmsTPresShList(RmsTPresSh rmsTPresSh);

    /**
     * 新增处方审核意见
     * 
     * @param rmsTPresSh 处方审核意见
     * @return 结果
     */
    public int insertRmsTPresSh(RmsTPresSh rmsTPresSh);

    /**
     * 修改处方审核意见
     * 
     * @param rmsTPresSh 处方审核意见
     * @return 结果
     */
    public int updateRmsTPresSh(RmsTPresSh rmsTPresSh);

    /**
     * 删除处方审核意见
     * 
     * @param id 处方审核意见主键
     * @return 结果
     */
    public int deleteRmsTPresShById(Long id);

    /**
     * 批量删除处方审核意见
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTPresShByIds(Long[] ids);
}
