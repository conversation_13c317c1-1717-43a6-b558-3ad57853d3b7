package com.rms.core.domain.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 处方信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class PrescriptionDataDTO {

    /**
     * 处方号
     */
    @NotBlank(message = "处方号不能为空")
    private String id;

    /**
     * 处方诊断节点
     */
    @Valid
    private List<DiagnoseDataDTO> presDiagnoseData;

    /**
     * 处方理由
     */
    private String reason;

    /**
     * 是否紧急处方：0-否；1-是（IPRC专用）
     */
    private Integer isUrgent;

    /**
     * 是否新开处方：0-否；1-是（IPRC专用）
     */
    private Integer isNew;

    /**
     * 是否当前处方：0-历史；1-当前
     */
    @NotNull(message = "是否当前处方不能为空")
    private Integer isCurrent;

    /**
     * 开嘱医生代码
     */
    private String doctCode;

    /**
     * 开嘱医生姓名
     */
    private String doctName;

    /**
     * 开嘱科室代码
     */
    private String deptCode;

    /**
     * 开嘱科室名称
     */
    private String deptName;

    /**
     * 医嘱类型：L-长期；T-临时（住院处方有效）
     */
    @NotBlank(message = "医嘱类型不能为空")
    private String presType;

    /**
     * 处方时间（YYYY-MM-DD HH:mm:SS）
     */
    @NotBlank(message = "处方时间不能为空")
    private String presTime;

    /**
     * 出院带药标识：0-否；1-是（IPRC专用）
     */
    private Integer dischargeDrug;

    /**
     * 处方类型：1-西药；2-中/草药；0-未定
     */
    @NotNull(message = "处方类型不能为空")
    private Integer prescriptionType;

    /**
     * 处方说明
     */
    private String presSm;

    /**
     * 中药处方/医嘱服用类型：1-内服；2-外敷；0-未定（中药用）
     */
    private Integer admType;

    /**
     * 要求（中药用）
     */
    private String requir;

    /**
     * 次数（中药用）
     */
    private Integer cs1;

    /**
     * 天数（中药用）
     */
    private Integer ts;

    /**
     * 煎煮溶剂（中药用）
     */
    private String solvent;

    /**
     * 剂量（中药用）
     */
    private String jl;

    /**
     * 次数（中药用）
     */
    private Integer cs2;

    /**
     * 煎药类别（中药用）
     */
    private String lb;

    /**
     * 方式1（中药用）
     */
    private String fs1;

    /**
     * 方式2（中药用）
     */
    private String fs2;

    /**
     * 协定处方标志：0-否；1-是
     */
    private Integer isXd;

    /**
     * 药品信息
     */
    @Valid
    @NotEmpty(message = "药品信息不能为空")
    private List<MedicineDataDTO> medicineData;

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public List<DiagnoseDataDTO> getPresDiagnoseData() {
        return presDiagnoseData;
    }

    public void setPresDiagnoseData(List<DiagnoseDataDTO> presDiagnoseData) {
        this.presDiagnoseData = presDiagnoseData;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public Integer getIsUrgent() {
        return isUrgent;
    }

    public void setIsUrgent(Integer isUrgent) {
        this.isUrgent = isUrgent;
    }

    public Integer getIsNew() {
        return isNew;
    }

    public void setIsNew(Integer isNew) {
        this.isNew = isNew;
    }

    public Integer getIsCurrent() {
        return isCurrent;
    }

    public void setIsCurrent(Integer isCurrent) {
        this.isCurrent = isCurrent;
    }

    public String getDoctCode() {
        return doctCode;
    }

    public void setDoctCode(String doctCode) {
        this.doctCode = doctCode;
    }

    public String getDoctName() {
        return doctName;
    }

    public void setDoctName(String doctName) {
        this.doctName = doctName;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getPresType() {
        return presType;
    }

    public void setPresType(String presType) {
        this.presType = presType;
    }

    public String getPresTime() {
        return presTime;
    }

    public void setPresTime(String presTime) {
        this.presTime = presTime;
    }

    public Integer getDischargeDrug() {
        return dischargeDrug;
    }

    public void setDischargeDrug(Integer dischargeDrug) {
        this.dischargeDrug = dischargeDrug;
    }

    public Integer getPrescriptionType() {
        return prescriptionType;
    }

    public void setPrescriptionType(Integer prescriptionType) {
        this.prescriptionType = prescriptionType;
    }

    public String getPresSm() {
        return presSm;
    }

    public void setPresSm(String presSm) {
        this.presSm = presSm;
    }

    public Integer getAdmType() {
        return admType;
    }

    public void setAdmType(Integer admType) {
        this.admType = admType;
    }

    public String getRequir() {
        return requir;
    }

    public void setRequir(String requir) {
        this.requir = requir;
    }

    public Integer getCs1() {
        return cs1;
    }

    public void setCs1(Integer cs1) {
        this.cs1 = cs1;
    }

    public Integer getTs() {
        return ts;
    }

    public void setTs(Integer ts) {
        this.ts = ts;
    }

    public String getSolvent() {
        return solvent;
    }

    public void setSolvent(String solvent) {
        this.solvent = solvent;
    }

    public String getJl() {
        return jl;
    }

    public void setJl(String jl) {
        this.jl = jl;
    }

    public Integer getCs2() {
        return cs2;
    }

    public void setCs2(Integer cs2) {
        this.cs2 = cs2;
    }

    public String getLb() {
        return lb;
    }

    public void setLb(String lb) {
        this.lb = lb;
    }

    public String getFs1() {
        return fs1;
    }

    public void setFs1(String fs1) {
        this.fs1 = fs1;
    }

    public String getFs2() {
        return fs2;
    }

    public void setFs2(String fs2) {
        this.fs2 = fs2;
    }

    public Integer getIsXd() {
        return isXd;
    }

    public void setIsXd(Integer isXd) {
        this.isXd = isXd;
    }

    public List<MedicineDataDTO> getMedicineData() {
        return medicineData;
    }

    public void setMedicineData(List<MedicineDataDTO> medicineData) {
        this.medicineData = medicineData;
    }
}
