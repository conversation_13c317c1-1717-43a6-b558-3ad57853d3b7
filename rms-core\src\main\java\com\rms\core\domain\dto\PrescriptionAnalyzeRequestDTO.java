package com.rms.core.domain.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * 处方分析请求DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class PrescriptionAnalyzeRequestDTO {

    /**
     * 基本信息
     */
    @Valid
    @NotNull(message = "基本信息不能为空")
    private BaseInfoDTO baseInfo;

    /**
     * 详细信息
     */
    @Valid
    @NotNull(message = "详细信息不能为空")
    private PrescriptionAnalyzeDetailsDTO details;

    public BaseInfoDTO getBaseInfo() {
        return baseInfo;
    }

    public void setBaseInfo(BaseInfoDTO baseInfo) {
        this.baseInfo = baseInfo;
    }

    public PrescriptionAnalyzeDetailsDTO getDetails() {
        return details;
    }

    public void setDetails(PrescriptionAnalyzeDetailsDTO details) {
        this.details = details;
    }

    @Override
    public String toString() {
        return "PrescriptionAnalyzeRequestDTO{" +
                "baseInfo=" + baseInfo +
                ", details=" + details +
                '}';
    }
}
