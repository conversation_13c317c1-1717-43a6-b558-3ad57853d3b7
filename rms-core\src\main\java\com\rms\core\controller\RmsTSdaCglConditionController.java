package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaCglCondition;
import com.rms.core.service.IRmsTSdaCglConditionService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 药品常规用量条件Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/rms/tsdacglcondition")
public class RmsTSdaCglConditionController extends BaseController
{
    @Autowired
    private IRmsTSdaCglConditionService rmsTSdaCglConditionService;

    /**
     * 查询药品常规用量条件列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglcondition:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaCglCondition rmsTSdaCglCondition)
    {
        startPage();
        List<RmsTSdaCglCondition> list = rmsTSdaCglConditionService.selectRmsTSdaCglConditionList(rmsTSdaCglCondition);
        return getDataTable(list);
    }

    /**
     * 导出药品常规用量条件列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglcondition:export')")
    @Log(title = "药品常规用量条件", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaCglCondition rmsTSdaCglCondition)
    {
        List<RmsTSdaCglCondition> list = rmsTSdaCglConditionService.selectRmsTSdaCglConditionList(rmsTSdaCglCondition);
        ExcelUtil<RmsTSdaCglCondition> util = new ExcelUtil<RmsTSdaCglCondition>(RmsTSdaCglCondition.class);
        util.exportExcel(response, list, "药品常规用量条件数据");
    }

    /**
     * 获取药品常规用量条件详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglcondition:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTSdaCglConditionService.selectRmsTSdaCglConditionById(id));
    }

    /**
     * 新增药品常规用量条件
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglcondition:add')")
    @Log(title = "药品常规用量条件", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaCglCondition rmsTSdaCglCondition)
    {
        return toAjax(rmsTSdaCglConditionService.insertRmsTSdaCglCondition(rmsTSdaCglCondition));
    }

    /**
     * 修改药品常规用量条件
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglcondition:edit')")
    @Log(title = "药品常规用量条件", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaCglCondition rmsTSdaCglCondition)
    {
        return toAjax(rmsTSdaCglConditionService.updateRmsTSdaCglCondition(rmsTSdaCglCondition));
    }

    /**
     * 删除药品常规用量条件
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdacglcondition:remove')")
    @Log(title = "药品常规用量条件", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTSdaCglConditionService.deleteRmsTSdaCglConditionByIds(ids));
    }
}
