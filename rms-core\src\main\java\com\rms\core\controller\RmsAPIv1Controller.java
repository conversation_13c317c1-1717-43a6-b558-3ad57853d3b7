package com.rms.core.controller;

import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.core.domain.dto.*;
import com.rms.core.service.IRmsApiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 合理用药与事前审方系统接口（v1）Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/api/v1")
@Validated
public class RmsAPIv1Controller extends BaseController
{
    @Autowired
    private IRmsApiService rmsApiService;

    /**
     * 处方分析接口
     * 医生开具处方后调用，用于分析处方合理性（如配伍禁忌、用药剂量、过敏风险等），返回问题级别及双签需求信息。
     *
     * @param request 处方分析请求
     * @return 处方分析结果
     */
    @PostMapping("/prescription/analyze")
    public AjaxResult analyzePrescription(@Valid @RequestBody PrescriptionAnalyzeRequestDTO request) {
        try {
            logger.info("收到处方分析请求，医院编码：{}，就诊号：{}",
                       request.getBaseInfo().getHospCode(),
                       request.getDetails().getTreatCode());

            PrescriptionAnalyzeResponseDTO response = rmsApiService.analyzePrescription(request);

            logger.info("处方分析完成，问题级别：{}，双签标志：{}，问题详情：{}",
                       response.getProblemLevel(),
                       response.getDoubleSignFlag(),
                       response.getProblemDetail());

            return AjaxResult.success("分析成功", response);

        } catch (Exception e) {
            logger.error("处方分析失败：{}", e.getMessage(), e);
            return AjaxResult.error("处方分析失败：" + e.getMessage());
        }
    }

    /**
     * 获取处方的审核结果
     * 用于查询指定处方的审核状态（如通过、待审核、不通过）。
     *
     * @param request 获取处方审核结果请求
     * @return 处方审核结果
     */
    @PostMapping("/prescription/check-result")
    public AjaxResult getPrescriptionCheckResult(@Valid @RequestBody PrescriptionCheckResultRequestDTO request) {
        try {
            logger.info("收到处方审核结果查询请求，医院编码：{}，就诊号：{}，处方号：{}",
                       request.getBaseInfo().getHospCode(),
                       request.getDetails().getTreatCode(),
                       request.getDetails().getPrescriptionId());

            PrescriptionCheckResultResponseDTO response = rmsApiService.getPrescriptionCheckResult(request);

            logger.info("处方审核结果查询完成，审核状态：{}", response.getCheckStatus());

            return AjaxResult.success("查询成功", response);

        } catch (Exception e) {
            logger.error("查询处方审核结果失败：{}", e.getMessage(), e);
            return AjaxResult.error("查询处方审核结果失败：" + e.getMessage());
        }
    }

    /**
     * 要点提示
     * 医生双击药品时调用，返回该药品的要点提示（如医保提示、抗菌提示、说明书等）。
     *
     * @param request 药品信息查询请求
     * @return 药品信息
     */
    @PostMapping("/medicine/info")
    public AjaxResult getMedicineInfo(@Valid @RequestBody MedicineInfoRequestDTO request) {
        try {
            logger.info("收到药品信息查询请求，医院编码：{}，药品代码：{}",
                       request.getBaseInfo().getHospCode(),
                       request.getDetails().getMedicine().getHisCode());

            MedicineInfoResponseDTO response = rmsApiService.getMedicineInfo(request);

            logger.info("药品信息查询完成，药品名称：{}",
                       response.getInfo() != null ? response.getInfo().getYm() : "未知");

            return AjaxResult.success("查询成功", response);

        } catch (Exception e) {
            logger.error("查询药品信息失败：{}", e.getMessage(), e);
            return AjaxResult.error("查询药品信息失败：" + e.getMessage());
        }
    }
}
