package com.rms.core.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.Arrays;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.common.core.page.TableDataInfo;
import com.rms.common.utils.SecurityUtils;
import com.rms.core.domain.RmsTPres;
import com.rms.core.domain.RmsTPresMed;
import com.rms.core.domain.RmsTPresFx;
import com.rms.core.domain.RmsTPresSh;
import com.rms.core.domain.RmsCfwtlb;
import com.rms.core.service.IRmsTPresService;
import com.rms.core.service.IRmsTPresMedService;
import com.rms.core.service.IRmsTPresFxService;
import com.rms.core.service.IRmsTPresShService;
import com.rms.core.service.IRmsCfwtlbService;
import com.rms.system.service.ISysConfigService;

/**
 * 处方审核Controller
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@RestController
@RequestMapping("/rms/prescription/review")
public class RmsPrescriptionReviewController extends BaseController
{
    @Autowired
    private IRmsTPresService rmsTPresService;

    @Autowired
    private IRmsTPresMedService rmsTPresMedService;

    @Autowired
    private IRmsTPresFxService rmsTPresFxService;

    @Autowired
    private IRmsTPresShService rmsTPresShService;

    @Autowired
    private IRmsCfwtlbService rmsCfwtlbService;

    @Autowired
    private ISysConfigService configService;

    /**
     * 查询待审核处方列表
     */
    @PreAuthorize("@ss.hasPermi('rms:prescription:review:list')")
    @GetMapping("/pending")
    public TableDataInfo getPendingPrescriptions(RmsTPres rmsTPres)
    {
        startPage();
        // 设置查询条件：flag=10表示进入审方中心的处方
        rmsTPres.setFlag(10L);
        List<RmsTPres> list = rmsTPresService.selectRmsTPresList(rmsTPres);
        return getDataTable(list);
    }

    /**
     * 查询处方详细信息（包含药品明细）
     */
    @PreAuthorize("@ss.hasPermi('rms:prescription:review:query')")
    @GetMapping("/detail/{code}")
    public AjaxResult getPrescriptionDetail(@PathVariable String code)
    {
        Map<String, Object> result = new HashMap<>();

        // 获取处方基本信息
        RmsTPres prescription = rmsTPresService.selectRmsTPresByCode(code);
        if (prescription == null) {
            return error("处方不存在");
        }
        result.put("prescription", prescription);

        // 获取药品明细
        RmsTPresMed queryMed = new RmsTPresMed();
        queryMed.setCode(code);
        List<RmsTPresMed> medications = rmsTPresMedService.selectRmsTPresMedList(queryMed);
        result.put("medications", medications);

        // 获取分析结果
        RmsTPresFx queryFx = new RmsTPresFx();
        queryFx.setCode(code);
        List<RmsTPresFx> analysisResults = rmsTPresFxService.selectRmsTPresFxList(queryFx);
        result.put("analysisResults", analysisResults);

        // 获取历史审核记录
        RmsTPresSh querySh = new RmsTPresSh();
        querySh.setCode(code);
        List<RmsTPresSh> reviewHistory = rmsTPresShService.selectRmsTPresShList(querySh);
        result.put("reviewHistory", reviewHistory);

        return success(result);
    }

    /**
     * 获取处方问题类别列表
     */
    @PreAuthorize("@ss.hasPermi('rms:prescription:review:query')")
    @GetMapping("/problemTypes")
    public AjaxResult getProblemTypes()
    {
        List<RmsCfwtlb> problemTypes = rmsCfwtlbService.selectRmsCfwtlbList(new RmsCfwtlb());
        return success(problemTypes);
    }

    /**
     * 获取系统刷新时间配置
     */
    @GetMapping("/refreshTime")
    public AjaxResult getRefreshTime()
    {
        String refreshTime = configService.selectConfigByKey("rms.pres.refreshTime");
        // 默认5秒
        int time = 5000;
        try {
            if (refreshTime != null && !refreshTime.isEmpty()) {
                time = Integer.parseInt(refreshTime) * 1000; // 转换为毫秒
            }
        } catch (NumberFormatException e) {
            logger.warn("刷新时间配置格式错误，使用默认值5秒");
        }
        return success(time);
    }

    /**
     * 批量审核通过
     */
    @PreAuthorize("@ss.hasPermi('rms:prescription:review:approve')")
    @Log(title = "处方审核", businessType = BusinessType.UPDATE)
    @PostMapping("/approve")
    public AjaxResult approvePrescriptions(@RequestBody List<String> codesList)
    {
        if (codesList == null || codesList.isEmpty()) {
            return error("请选择要审核的处方");
        }

        // 转换为数组
        String[] codes = codesList.toArray(new String[0]);

        try {
            // 批量更新处方状态为审核通过(12)
            int updateCount = rmsTPresService.batchUpdatePrescriptionStatus(codes, 12L);

            // 记录审核历史
            Long userId = SecurityUtils.getUserId();
            String nickName = SecurityUtils.getUsername();

            for (String code : codes) {
                RmsTPresSh reviewRecord = new RmsTPresSh();
                reviewRecord.setCode(code);
                reviewRecord.setFlag(12L);
                reviewRecord.setText("审核通过");
                reviewRecord.setUserId(userId);
                reviewRecord.setNickName(nickName);
                rmsTPresShService.insertRmsTPresSh(reviewRecord);
            }

            return success("成功审核通过 " + updateCount + " 条处方");
        } catch (Exception e) {
            logger.error("审核通过操作失败", e);
            return error("审核操作失败：" + e.getMessage());
        }
    }

    /**
     * 批量审核打回
     */
    @PreAuthorize("@ss.hasPermi('rms:prescription:review:reject')")
    @Log(title = "处方审核", businessType = BusinessType.UPDATE)
    @PostMapping("/reject")
    public AjaxResult rejectPrescriptions(@RequestBody Map<String, Object> params)
    {
        // 安全地转换codes参数
        String[] codes = null;
        Object codesObj = params.get("codes");
        if (codesObj instanceof String[]) {
            codes = (String[]) codesObj;
        } else if (codesObj instanceof List) {
            List<?> codesList = (List<?>) codesObj;
            codes = codesList.toArray(new String[0]);
        }

        // 安全地转换problemNames参数
        String[] problemNames = null;
        Object problemNamesObj = params.get("problemNames");
        if (problemNamesObj instanceof String[]) {
            problemNames = (String[]) problemNamesObj;
        } else if (problemNamesObj instanceof List) {
            List<?> problemNamesList = (List<?>) problemNamesObj;
            problemNames = problemNamesList.toArray(new String[0]);
        }

        if (codes == null || codes.length == 0) {
            return error("请选择要打回的处方");
        }

        if (problemNames == null || problemNames.length == 0) {
            return error("请选择问题类型");
        }

        try {
            // 合并问题名称
            String problemText = String.join("；", problemNames);

            // 批量更新处方状态为审核不通过(11)
            int updateCount = rmsTPresService.batchUpdatePrescriptionStatus(codes, 11L);

            // 记录审核历史
            Long userId = SecurityUtils.getUserId();
            String nickName = SecurityUtils.getUsername();

            for (String code : codes) {
                RmsTPresSh reviewRecord = new RmsTPresSh();
                reviewRecord.setCode(code);
                reviewRecord.setFlag(11L);
                reviewRecord.setText(problemText);
                reviewRecord.setUserId(userId);
                reviewRecord.setNickName(nickName);
                rmsTPresShService.insertRmsTPresSh(reviewRecord);
            }

            return success("成功打回 " + updateCount + " 条处方");
        } catch (Exception e) {
            logger.error("审核打回操作失败", e);
            return error("审核操作失败：" + e.getMessage());
        }
    }

    /**
     * 获取科室列表（用于筛选）
     */
    @GetMapping("/departments")
    public AjaxResult getDepartments()
    {
        // 查询所有有处方的科室
        List<Map<String, Object>> departments = rmsTPresService.selectDistinctDepartments();
        return success(departments);
    }
}
