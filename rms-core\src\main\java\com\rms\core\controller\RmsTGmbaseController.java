package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTGmbase;
import com.rms.core.service.IRmsTGmbaseService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 过敏基础信息Controller
 *
 * <AUTHOR>
 * @date 2025-07-12
 */
@RestController
@RequestMapping("/rms/tgmbase")
public class RmsTGmbaseController extends BaseController
{
    @Autowired
    private IRmsTGmbaseService rmsTGmbaseService;

    /**
     * 查询过敏基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tgmbase:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTGmbase rmsTGmbase)
    {
        startPage();
        List<RmsTGmbase> list = rmsTGmbaseService.selectRmsTGmbaseList(rmsTGmbase);
        return getDataTable(list);
    }

    /**
     * 导出过敏基础信息列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tgmbase:export')")
    @Log(title = "过敏基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTGmbase rmsTGmbase)
    {
        List<RmsTGmbase> list = rmsTGmbaseService.selectRmsTGmbaseList(rmsTGmbase);
        ExcelUtil<RmsTGmbase> util = new ExcelUtil<RmsTGmbase>(RmsTGmbase.class);
        util.exportExcel(response, list, "过敏基础信息数据");
    }

    /**
     * 获取过敏基础信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tgmbase:query')")
    @GetMapping(value = "/{code}")
    public AjaxResult getInfo(@PathVariable("code") String code)
    {
        return success(rmsTGmbaseService.selectRmsTGmbaseByCode(code));
    }

    /**
     * 新增过敏基础信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tgmbase:add')")
    @Log(title = "过敏基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTGmbase rmsTGmbase)
    {
        return toAjax(rmsTGmbaseService.insertRmsTGmbase(rmsTGmbase));
    }

    /**
     * 修改过敏基础信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tgmbase:edit')")
    @Log(title = "过敏基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTGmbase rmsTGmbase)
    {
        return toAjax(rmsTGmbaseService.updateRmsTGmbase(rmsTGmbase));
    }

    /**
     * 删除过敏基础信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tgmbase:remove')")
    @Log(title = "过敏基础信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{codes}")
    public AjaxResult remove(@PathVariable String[] codes)
    {
        return toAjax(rmsTGmbaseService.deleteRmsTGmbaseByCodes(codes));
    }
}
