package com.rms.core.mapper;

import java.util.List;
import java.util.Map;
import com.rms.core.domain.RmsTSdaGm;

/**
 * 药品过敏信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-12
 */
public interface RmsTSdaGmMapper 
{
    /**
     * 查询药品过敏信息
     * 
     * @param id 药品过敏信息主键
     * @return 药品过敏信息
     */
    public RmsTSdaGm selectRmsTSdaGmById(Long id);

    /**
     * 查询药品过敏信息列表
     * 
     * @param rmsTSdaGm 药品过敏信息
     * @return 药品过敏信息集合
     */
    public List<RmsTSdaGm> selectRmsTSdaGmList(RmsTSdaGm rmsTSdaGm);

    /**
     * 新增药品过敏信息
     * 
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    public int insertRmsTSdaGm(RmsTSdaGm rmsTSdaGm);

    /**
     * 修改药品过敏信息
     * 
     * @param rmsTSdaGm 药品过敏信息
     * @return 结果
     */
    public int updateRmsTSdaGm(RmsTSdaGm rmsTSdaGm);

    /**
     * 删除药品过敏信息
     * 
     * @param id 药品过敏信息主键
     * @return 结果
     */
    public int deleteRmsTSdaGmById(Long id);

    /**
     * 批量删除药品过敏信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaGmByIds(Long[] ids);

    /**
     * 根据sdaId查询药品过敏信息（包含过敏名称）
     * 
     * @param sdaId 标准数据ID
     * @return 药品过敏信息集合（包含过敏名称）
     */
    public List<Map<String, Object>> selectAllergyRulesWithNameBySdaId(Long sdaId);

    /**
     * 根据关键字搜索药品过敏信息
     * 
     * @param keyword 搜索关键字
     * @return 药品过敏信息集合
     */
    public List<Map<String, Object>> searchByKeyword(String keyword);
}
