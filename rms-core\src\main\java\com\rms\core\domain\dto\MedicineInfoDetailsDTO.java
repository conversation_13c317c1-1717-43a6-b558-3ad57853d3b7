package com.rms.core.domain.dto;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 药品信息查询详细信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class MedicineInfoDetailsDTO {

    /**
     * 门诊/住院标识：op-门诊；ip-住院
     */
    @NotBlank(message = "门诊/住院标识不能为空")
    private String hospFlag;

    /**
     * 药品信息
     */
    @Valid
    @NotNull(message = "药品信息不能为空")
    private MedicineDTO medicine;

    public String getHospFlag() {
        return hospFlag;
    }

    public void setHospFlag(String hospFlag) {
        this.hospFlag = hospFlag;
    }

    public MedicineDTO getMedicine() {
        return medicine;
    }

    public void setMedicine(MedicineDTO medicine) {
        this.medicine = medicine;
    }

    @Override
    public String toString() {
        return "MedicineInfoDetailsDTO{" +
                "hospFlag='" + hospFlag + '\'' +
                ", medicine=" + medicine +
                '}';
    }
}
