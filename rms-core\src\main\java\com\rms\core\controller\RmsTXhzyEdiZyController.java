package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTXhzyEdiZy;
import com.rms.core.service.IRmsTXhzyEdiZyService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 中药相互作用Controller
 *
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/rms/txhzyedizy")
public class RmsTXhzyEdiZyController extends BaseController
{
    @Autowired
    private IRmsTXhzyEdiZyService rmsTXhzyEdiZyService;

    /**
     * 查询中药相互作用列表
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedizy:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        startPage();
        List<RmsTXhzyEdiZy> list = rmsTXhzyEdiZyService.selectRmsTXhzyEdiZyList(rmsTXhzyEdiZy);
        return getDataTable(list);
    }

    /**
     * 导出中药相互作用列表
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedizy:export')")
    @Log(title = "中药相互作用", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        List<RmsTXhzyEdiZy> list = rmsTXhzyEdiZyService.selectRmsTXhzyEdiZyList(rmsTXhzyEdiZy);
        ExcelUtil<RmsTXhzyEdiZy> util = new ExcelUtil<RmsTXhzyEdiZy>(RmsTXhzyEdiZy.class);
        util.exportExcel(response, list, "中药相互作用数据");
    }

    /**
     * 获取中药相互作用详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedizy:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTXhzyEdiZyService.selectRmsTXhzyEdiZyById(id));
    }

    /**
     * 新增中药相互作用
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedizy:add')")
    @Log(title = "中药相互作用", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        return toAjax(rmsTXhzyEdiZyService.insertRmsTXhzyEdiZy(rmsTXhzyEdiZy));
    }

    /**
     * 修改中药相互作用
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedizy:edit')")
    @Log(title = "中药相互作用", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTXhzyEdiZy rmsTXhzyEdiZy)
    {
        return toAjax(rmsTXhzyEdiZyService.updateRmsTXhzyEdiZy(rmsTXhzyEdiZy));
    }

    /**
     * 删除中药相互作用
     */
    @PreAuthorize("@ss.hasPermi('rms:txhzyedizy:remove')")
    @Log(title = "中药相互作用", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTXhzyEdiZyService.deleteRmsTXhzyEdiZyByIds(ids));
    }
}
