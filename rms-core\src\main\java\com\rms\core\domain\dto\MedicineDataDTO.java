package com.rms.core.domain.dto;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 药品信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class MedicineDataDTO {

    /**
     * 商品名
     */
    @NotBlank(message = "药品商品名不能为空")
    private String name;

    /**
     * 医院药品代码
     */
    @NotBlank(message = "医院药品代码不能为空")
    private String hisCode;

    /**
     * 医保代码
     */
    private String insurCode;

    /**
     * 批准文号
     */
    private String approval;

    /**
     * 配液单号
     */
    private String pydCode;

    /**
     * 配液单组号，组号不能只传1，应该传同组第一个药品的医嘱号
     */
    private String linkGroup;

    /**
     * 规格
     */
    private String spec;

    /**
     * 组号
     */
    @NotBlank(message = "组号不能为空")
    private String group;

    /**
     * 用药理由
     */
    private String reason;

    /**
     * 单次量单位
     */
    @NotBlank(message = "单次量单位不能为空")
    private String doseUnit;

    /**
     * 单次量
     */
    @NotNull(message = "单次量不能为空")
    private Double dose;

    /**
     * 开药数量（IPRC专用）
     */
    private Double ordQty;

    /**
     * 开药数量单位（IPRC专用）
     */
    private String ordUom;

    /**
     * 频次代码
     */
    @NotBlank(message = "频次代码不能为空")
    private String freq;

    /**
     * 给药途径代码
     */
    @NotBlank(message = "给药途径代码不能为空")
    private String administer;

    /**
     * 用药开始时间（住院用）
     */
    @NotBlank(message = "用药开始时间不能为空")
    private String beginTime;

    /**
     * 用药结束时间（住院用）
     */
    @NotBlank(message = "用药结束时间不能为空")
    private String endTime;

    /**
     * 服药天数（门诊用）
     */
    private Integer days;

    /**
     * 金额
     */
    private Double money;

    /**
     * 用药说明
     */
    private String yysm;

    /**
     * 备注
     */
    private String bz;

    /**
     * 取药地点
     */
    private String qydd;

    /**
     * 是否预防用药：1-是；0-否（住院用）
     */
    private Integer preventiveflag;

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHisCode() {
        return hisCode;
    }

    public void setHisCode(String hisCode) {
        this.hisCode = hisCode;
    }

    public String getInsurCode() {
        return insurCode;
    }

    public void setInsurCode(String insurCode) {
        this.insurCode = insurCode;
    }

    public String getApproval() {
        return approval;
    }

    public void setApproval(String approval) {
        this.approval = approval;
    }

    public String getPydCode() {
        return pydCode;
    }

    public void setPydCode(String pydCode) {
        this.pydCode = pydCode;
    }

    public String getLinkGroup() {
        return linkGroup;
    }

    public void setLinkGroup(String linkGroup) {
        this.linkGroup = linkGroup;
    }

    public String getSpec() {
        return spec;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getDoseUnit() {
        return doseUnit;
    }

    public void setDoseUnit(String doseUnit) {
        this.doseUnit = doseUnit;
    }

    public Double getDose() {
        return dose;
    }

    public void setDose(Double dose) {
        this.dose = dose;
    }

    public Double getOrdQty() {
        return ordQty;
    }

    public void setOrdQty(Double ordQty) {
        this.ordQty = ordQty;
    }

    public String getOrdUom() {
        return ordUom;
    }

    public void setOrdUom(String ordUom) {
        this.ordUom = ordUom;
    }

    public String getFreq() {
        return freq;
    }

    public void setFreq(String freq) {
        this.freq = freq;
    }

    public String getAdminister() {
        return administer;
    }

    public void setAdminister(String administer) {
        this.administer = administer;
    }

    public String getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(String beginTime) {
        this.beginTime = beginTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public Integer getDays() {
        return days;
    }

    public void setDays(Integer days) {
        this.days = days;
    }

    public Double getMoney() {
        return money;
    }

    public void setMoney(Double money) {
        this.money = money;
    }

    public String getYysm() {
        return yysm;
    }

    public void setYysm(String yysm) {
        this.yysm = yysm;
    }

    public String getBz() {
        return bz;
    }

    public void setBz(String bz) {
        this.bz = bz;
    }

    public String getQydd() {
        return qydd;
    }

    public void setQydd(String qydd) {
        this.qydd = qydd;
    }

    public Integer getPreventiveflag() {
        return preventiveflag;
    }

    public void setPreventiveflag(Integer preventiveflag) {
        this.preventiveflag = preventiveflag;
    }

    @Override
    public String toString() {
        return "MedicineDataDTO{" +
                "name='" + name + '\'' +
                ", hisCode='" + hisCode + '\'' +
                ", insurCode='" + insurCode + '\'' +
                ", approval='" + approval + '\'' +
                ", pydCode='" + pydCode + '\'' +
                ", linkGroup='" + linkGroup + '\'' +
                ", spec='" + spec + '\'' +
                ", group='" + group + '\'' +
                ", reason='" + reason + '\'' +
                ", doseUnit='" + doseUnit + '\'' +
                ", dose=" + dose +
                ", ordQty=" + ordQty +
                ", ordUom='" + ordUom + '\'' +
                ", freq='" + freq + '\'' +
                ", administer='" + administer + '\'' +
                ", beginTime='" + beginTime + '\'' +
                ", endTime='" + endTime + '\'' +
                ", days=" + days +
                ", money=" + money +
                ", yysm='" + yysm + '\'' +
                ", bz='" + bz + '\'' +
                ", qydd='" + qydd + '\'' +
                ", preventiveflag=" + preventiveflag +
                '}';
    }
}
