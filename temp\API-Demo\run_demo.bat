@echo off
REM RMS API Demo 运行脚本 (Windows)
REM 
REM 使用说明：
REM 1. 确保Java已安装并配置环境变量
REM 2. 确保RMS系统已启动在 http://localhost:8080
REM 3. 双击运行此脚本或在命令行执行：run_demo.bat

echo ================================================================================
echo RMS API Demo 运行脚本
echo ================================================================================

REM 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误：未找到Java环境，请确保Java已正确安装并配置环境变量
    pause
    exit /b 1
)

echo ✅ Java环境检查通过

REM 编译Java文件
echo.
echo 📦 正在编译 RmsApiDemo.java...
javac RmsApiDemo.java
if %errorlevel% neq 0 (
    echo ❌ 编译失败，请检查代码是否有语法错误
    pause
    exit /b 1
)

echo ✅ 编译成功

REM 运行程序
echo.
echo 🚀 正在运行 RMS API Demo...
echo.
java RmsApiDemo

REM 清理编译文件
echo.
echo 🧹 清理编译文件...
del RmsApiDemo.class >nul 2>&1

echo.
echo ================================================================================
echo Demo 运行完成
echo ================================================================================
pause
