package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaSex;
import com.rms.core.service.IRmsTSdaSexService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 标准药品性别库Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/rms/tsdasex")
public class RmsTSdaSexController extends BaseController
{
    @Autowired
    private IRmsTSdaSexService rmsTSdaSexService;

    /**
     * 查询标准药品性别库列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdasex:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaSex rmsTSdaSex)
    {
        startPage();
        List<RmsTSdaSex> list = rmsTSdaSexService.selectRmsTSdaSexList(rmsTSdaSex);
        return getDataTable(list);
    }

    /**
     * 导出标准药品性别库列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdasex:export')")
    @Log(title = "标准药品性别库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaSex rmsTSdaSex)
    {
        List<RmsTSdaSex> list = rmsTSdaSexService.selectRmsTSdaSexList(rmsTSdaSex);
        ExcelUtil<RmsTSdaSex> util = new ExcelUtil<RmsTSdaSex>(RmsTSdaSex.class);
        util.exportExcel(response, list, "标准药品性别库数据");
    }

    /**
     * 获取标准药品性别库详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdasex:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTSdaSexService.selectRmsTSdaSexById(id));
    }

    /**
     * 新增标准药品性别库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdasex:add')")
    @Log(title = "标准药品性别库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaSex rmsTSdaSex)
    {
        return toAjax(rmsTSdaSexService.insertRmsTSdaSex(rmsTSdaSex));
    }

    /**
     * 修改标准药品性别库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdasex:edit')")
    @Log(title = "标准药品性别库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaSex rmsTSdaSex)
    {
        return toAjax(rmsTSdaSexService.updateRmsTSdaSex(rmsTSdaSex));
    }

    /**
     * 删除标准药品性别库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdasex:remove')")
    @Log(title = "标准药品性别库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTSdaSexService.deleteRmsTSdaSexByIds(ids));
    }
}
