package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTSdaGestation;

/**
 * 孕期用药规则库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-10
 */
public interface RmsTSdaGestationMapper 
{
    /**
     * 查询孕期用药规则库
     * 
     * @param id 孕期用药规则库主键
     * @return 孕期用药规则库
     */
    public RmsTSdaGestation selectRmsTSdaGestationById(Long id);

    /**
     * 查询孕期用药规则库列表
     * 
     * @param rmsTSdaGestation 孕期用药规则库
     * @return 孕期用药规则库集合
     */
    public List<RmsTSdaGestation> selectRmsTSdaGestationList(RmsTSdaGestation rmsTSdaGestation);

    /**
     * 新增孕期用药规则库
     * 
     * @param rmsTSdaGestation 孕期用药规则库
     * @return 结果
     */
    public int insertRmsTSdaGestation(RmsTSdaGestation rmsTSdaGestation);

    /**
     * 修改孕期用药规则库
     * 
     * @param rmsTSdaGestation 孕期用药规则库
     * @return 结果
     */
    public int updateRmsTSdaGestation(RmsTSdaGestation rmsTSdaGestation);

    /**
     * 删除孕期用药规则库
     * 
     * @param id 孕期用药规则库主键
     * @return 结果
     */
    public int deleteRmsTSdaGestationById(Long id);

    /**
     * 批量删除孕期用药规则库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaGestationByIds(Long[] ids);
}
