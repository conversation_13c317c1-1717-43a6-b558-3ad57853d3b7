package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTSdaChd;

/**
 * 儿童用药规则库Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface RmsTSdaChdMapper 
{
    /**
     * 查询儿童用药规则库
     * 
     * @param id 儿童用药规则库主键
     * @return 儿童用药规则库
     */
    public RmsTSdaChd selectRmsTSdaChdById(Long id);

    /**
     * 查询儿童用药规则库列表
     * 
     * @param rmsTSdaChd 儿童用药规则库
     * @return 儿童用药规则库集合
     */
    public List<RmsTSdaChd> selectRmsTSdaChdList(RmsTSdaChd rmsTSdaChd);

    /**
     * 新增儿童用药规则库
     * 
     * @param rmsTSdaChd 儿童用药规则库
     * @return 结果
     */
    public int insertRmsTSdaChd(RmsTSdaChd rmsTSdaChd);

    /**
     * 修改儿童用药规则库
     * 
     * @param rmsTSdaChd 儿童用药规则库
     * @return 结果
     */
    public int updateRmsTSdaChd(RmsTSdaChd rmsTSdaChd);

    /**
     * 删除儿童用药规则库
     * 
     * @param id 儿童用药规则库主键
     * @return 结果
     */
    public int deleteRmsTSdaChdById(Long id);

    /**
     * 批量删除儿童用药规则库
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTSdaChdByIds(Long[] ids);
}
