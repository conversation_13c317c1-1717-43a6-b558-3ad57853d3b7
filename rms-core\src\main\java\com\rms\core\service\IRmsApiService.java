package com.rms.core.service;

import com.rms.core.domain.dto.*;

/**
 * 合理用药与事前审方系统API Service接口
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public interface IRmsApiService {

    /**
     * 处方分析
     * 
     * @param request 处方分析请求
     * @return 处方分析结果
     */
    PrescriptionAnalyzeResponseDTO analyzePrescription(PrescriptionAnalyzeRequestDTO request);

    /**
     * 获取处方审核结果
     * 
     * @param request 获取处方审核结果请求
     * @return 处方审核结果
     */
    PrescriptionCheckResultResponseDTO getPrescriptionCheckResult(PrescriptionCheckResultRequestDTO request);

    /**
     * 获取药品信息
     * 
     * @param request 药品信息查询请求
     * @return 药品信息
     */
    MedicineInfoResponseDTO getMedicineInfo(MedicineInfoRequestDTO request);
}
