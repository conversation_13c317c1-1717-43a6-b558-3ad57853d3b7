package com.rms.core.domain.dto;

import javax.validation.constraints.NotBlank;

/**
 * 药品信息DTO
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
public class MedicineDTO {

    /**
     * 医院药品代码
     */
    @NotBlank(message = "医院药品代码不能为空")
    private String hisCode;

    /**
     * 药品名称
     */
    private String hisName;

    public String getHisCode() {
        return hisCode;
    }

    public void setHisCode(String hisCode) {
        this.hisCode = hisCode;
    }

    public String getHisName() {
        return hisName;
    }

    public void setHisName(String hisName) {
        this.hisName = hisName;
    }

    @Override
    public String toString() {
        return "MedicineDTO{" +
                "hisCode='" + hisCode + '\'' +
                ", hisName='" + hisName + '\'' +
                '}';
    }
}
