package com.rms.core.mapper;

import java.util.List;
import com.rms.core.domain.RmsTXhzyEdi;

/**
 * 药品相互作用Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface RmsTXhzyEdiMapper 
{
    /**
     * 查询药品相互作用
     * 
     * @param id 药品相互作用主键
     * @return 药品相互作用
     */
    public RmsTXhzyEdi selectRmsTXhzyEdiById(Long id);

    /**
     * 查询药品相互作用列表
     * 
     * @param rmsTXhzyEdi 药品相互作用
     * @return 药品相互作用集合
     */
    public List<RmsTXhzyEdi> selectRmsTXhzyEdiList(RmsTXhzyEdi rmsTXhzyEdi);

    /**
     * 新增药品相互作用
     * 
     * @param rmsTXhzyEdi 药品相互作用
     * @return 结果
     */
    public int insertRmsTXhzyEdi(RmsTXhzyEdi rmsTXhzyEdi);

    /**
     * 修改药品相互作用
     * 
     * @param rmsTXhzyEdi 药品相互作用
     * @return 结果
     */
    public int updateRmsTXhzyEdi(RmsTXhzyEdi rmsTXhzyEdi);

    /**
     * 删除药品相互作用
     * 
     * @param id 药品相互作用主键
     * @return 结果
     */
    public int deleteRmsTXhzyEdiById(Long id);

    /**
     * 批量删除药品相互作用
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRmsTXhzyEdiByIds(Long[] ids);

    /**
     * 根据sdaId查询配伍禁忌信息（包含药物名称）
     * 
     * @param sdaId 标准数据ID
     * @return 配伍禁忌信息集合
     */
    public List<com.rms.core.domain.IncompatibleDrugResult> selectIncompatibleRulesBySdaId(Long sdaId);
}
