import request from '@/utils/request'

// 查询待审核处方列表
export function getPendingPrescriptions(query) {
  return request({
    url: '/rms/prescription/review/pending',
    method: 'get',
    params: query
  })
}

// 查询处方详细信息
export function getPrescriptionDetail(code) {
  return request({
    url: '/rms/prescription/review/detail/' + code,
    method: 'get'
  })
}

// 获取处方问题类别列表
export function getProblemTypes() {
  return request({
    url: '/rms/prescription/review/problemTypes',
    method: 'get'
  })
}

// 获取系统刷新时间配置
export function getRefreshTime() {
  return request({
    url: '/rms/prescription/review/refreshTime',
    method: 'get'
  })
}

// 批量审核通过
export function approvePrescriptions(codes) {
  return request({
    url: '/rms/prescription/review/approve',
    method: 'post',
    data: codes
  })
}

// 批量审核打回
export function rejectPrescriptions(data) {
  return request({
    url: '/rms/prescription/review/reject',
    method: 'post',
    data: data
  })
}

// 获取科室列表
export function getDepartments() {
  return request({
    url: '/rms/prescription/review/departments',
    method: 'get'
  })
}
