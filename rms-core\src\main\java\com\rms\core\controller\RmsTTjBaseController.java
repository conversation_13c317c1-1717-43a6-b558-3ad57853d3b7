package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTTjBase;
import com.rms.core.service.IRmsTTjBaseService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 给药途径基础表Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/rms/ttjbase")
public class RmsTTjBaseController extends BaseController
{
    @Autowired
    private IRmsTTjBaseService rmsTTjBaseService;

    /**
     * 查询给药途径基础表列表
     */
    @PreAuthorize("@ss.hasPermi('rms:ttjbase:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTTjBase rmsTTjBase)
    {
        startPage();
        List<RmsTTjBase> list = rmsTTjBaseService.selectRmsTTjBaseList(rmsTTjBase);
        return getDataTable(list);
    }

    /**
     * 导出给药途径基础表列表
     */
    @PreAuthorize("@ss.hasPermi('rms:ttjbase:export')")
    @Log(title = "给药途径基础表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTTjBase rmsTTjBase)
    {
        List<RmsTTjBase> list = rmsTTjBaseService.selectRmsTTjBaseList(rmsTTjBase);
        ExcelUtil<RmsTTjBase> util = new ExcelUtil<RmsTTjBase>(RmsTTjBase.class);
        util.exportExcel(response, list, "给药途径基础表数据");
    }

    /**
     * 获取给药途径基础表详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:ttjbase:query')")
    @GetMapping(value = "/{name}")
    public AjaxResult getInfo(@PathVariable("name") String name)
    {
        return success(rmsTTjBaseService.selectRmsTTjBaseByName(name));
    }

    /**
     * 新增给药途径基础表
     */
    @PreAuthorize("@ss.hasPermi('rms:ttjbase:add')")
    @Log(title = "给药途径基础表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTTjBase rmsTTjBase)
    {
        return toAjax(rmsTTjBaseService.insertRmsTTjBase(rmsTTjBase));
    }

    /**
     * 修改给药途径基础表
     */
    @PreAuthorize("@ss.hasPermi('rms:ttjbase:edit')")
    @Log(title = "给药途径基础表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTTjBase rmsTTjBase)
    {
        return toAjax(rmsTTjBaseService.updateRmsTTjBase(rmsTTjBase));
    }

    /**
     * 删除给药途径基础表
     */
    @PreAuthorize("@ss.hasPermi('rms:ttjbase:remove')")
    @Log(title = "给药途径基础表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{names}")
    public AjaxResult remove(@PathVariable String[] names)
    {
        return toAjax(rmsTTjBaseService.deleteRmsTTjBaseByNames(names));
    }
}
