package com.rms.core.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.rms.common.annotation.Log;
import com.rms.common.core.controller.BaseController;
import com.rms.common.core.domain.AjaxResult;
import com.rms.common.enums.BusinessType;
import com.rms.core.domain.RmsTSdaGestation;
import com.rms.core.service.IRmsTSdaGestationService;
import com.rms.common.utils.poi.ExcelUtil;
import com.rms.common.core.page.TableDataInfo;

/**
 * 孕期用药规则库Controller
 *
 * <AUTHOR>
 * @date 2025-07-10
 */
@RestController
@RequestMapping("/rms/tsdagestation")
public class RmsTSdaGestationController extends BaseController
{
    @Autowired
    private IRmsTSdaGestationService rmsTSdaGestationService;

    /**
     * 查询孕期用药规则库列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagestation:list')")
    @GetMapping("/list")
    public TableDataInfo list(RmsTSdaGestation rmsTSdaGestation)
    {
        startPage();
        List<RmsTSdaGestation> list = rmsTSdaGestationService.selectRmsTSdaGestationList(rmsTSdaGestation);
        return getDataTable(list);
    }

    /**
     * 导出孕期用药规则库列表
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagestation:export')")
    @Log(title = "孕期用药规则库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, RmsTSdaGestation rmsTSdaGestation)
    {
        List<RmsTSdaGestation> list = rmsTSdaGestationService.selectRmsTSdaGestationList(rmsTSdaGestation);
        ExcelUtil<RmsTSdaGestation> util = new ExcelUtil<RmsTSdaGestation>(RmsTSdaGestation.class);
        util.exportExcel(response, list, "孕期用药规则库数据");
    }

    /**
     * 获取孕期用药规则库详细信息
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagestation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(rmsTSdaGestationService.selectRmsTSdaGestationById(id));
    }

    /**
     * 新增孕期用药规则库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagestation:add')")
    @Log(title = "孕期用药规则库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody RmsTSdaGestation rmsTSdaGestation)
    {
        return toAjax(rmsTSdaGestationService.insertRmsTSdaGestation(rmsTSdaGestation));
    }

    /**
     * 修改孕期用药规则库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagestation:edit')")
    @Log(title = "孕期用药规则库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody RmsTSdaGestation rmsTSdaGestation)
    {
        return toAjax(rmsTSdaGestationService.updateRmsTSdaGestation(rmsTSdaGestation));
    }

    /**
     * 删除孕期用药规则库
     */
    @PreAuthorize("@ss.hasPermi('rms:tsdagestation:remove')")
    @Log(title = "孕期用药规则库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(rmsTSdaGestationService.deleteRmsTSdaGestationByIds(ids));
    }
}
