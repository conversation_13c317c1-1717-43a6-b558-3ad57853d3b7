// MSSQL存储过程：
-- fx_syz 'bb536a55-4b58-4509-a282-8a9887de34b3','380','','R57.000;'
ALTER PROCEDURE [dbo].[fx_syz]
-- syzfx '380','55',''
--调用方法：传入医院编号、药品代码（单个药品）、所有诊断，各个诊断之间用;分割

@code nvarchar(50),
@akb020 nvarchar(20),
@yp_code nvarchar(20),--该参数无用，仅用来补位
@icd10_codes nvarchar(500)

---@fxms nvarchar(20)
AS
--return
declare @sda_id nvarchar(10)
declare @diagnoses nvarchar(10)
declare @n_count int
declare @n_count1 int
declare @n_count2 int
declare @n_count3 int
declare @n_count4 int
declare @n_count5 int
declare @n_count6 int
declare @n_count7 int
declare @n_count8 int
declare @n_count9 int
declare @n_count10 int
declare @n_count11 int
declare @icd10_code nvarchar(20)
declare @n_count12 int
declare @n_count13 int
declare @n_count14 int
declare @n_count133 int
declare @n_count144 int
declare @n_count17 int
declare @n_count18 int
declare @zdxx nvarchar(500)
declare @icd10_name nvarchar(500)
declare @remark nvarchar(500)
declare @ywa_name nvarchar(50)


begin try

declare @hosp_flag nvarchar(20)
--set @n_count1 =0

select @n_count6=count(1) from t_pres_med a
inner join ITF_HOS_DRUG b on b.DRUG_CODE=a.his_code
where a.Code=@code and b.ZX_FLAG='3'

if @n_count6 >0
begin
return
end

select @hosp_flag=hosp_flag,@zdxx=dbo.get_zdxx_cj(dia_info) from t_pres where code =@code
if @hosp_flag ='ip'
begin
select top 0 * from t_pres_fx
return
end



select @n_count13=count(distinct his_code) from  t_pres_med where code =@Code
select  @n_count12=count(distinct a.his_code) from  t_pres_med a,t_byyydzb b,t_sda_nosyz_lk c  where a.code =@Code  and a.his_code=b.yp_code and b.sda_id=c.sda_id
if @n_count13 = @n_count12
begin
return
end

select @n_count133=count(1)from t_pres where code =@code and (dia_info like '%炎%' or dia_info like '%感染%')
select @n_count144=count(1) from itf_hos_drug where (DRUG_CODE in (select his_code from t_pres_med where code =@code) and IS_ANTIBAC ='1'  )
if @n_count133>0 and @n_count144>0
begin
return
end

set @n_count1 =0

CREATE TABLE #t_pres_fx_ls(
	[Code] [nvarchar](50) NULL,
	[ywa] [nvarchar](50) NULL,
	[ywb] [nvarchar](50) NULL,
	[wtlvlcode] [int] NULL,
	[wtlvl] [nvarchar](5) NULL,
	[wtcode] [nvarchar](8) NULL,
	[wtsp] [nvarchar](30) NULL,
	[wtname] [nvarchar](30) NULL,
	[title] [nvarchar](500) NULL,
	[detail] [nvarchar](1000) NULL,
	[flag] [nvarchar](2) NULL,
	[text] [nvarchar](20) NULL
)








----解决动态库23函数循环调用不正确的问题
delete from t_pres_fx where code=@code and wtcode ='RLT037'
--insert into test values (@icd10_codes);

if @icd10_codes is null or @icd10_codes =''
begin
select @code,'' ywa,''ywb,'1'wtlvlcode,(select distinct d.wtlvl from wtlb d where d.wtlvlcode='2') wtlvl,
'RLT037' wtcode,'ZDXGYWSY'wtsp,'诊断与药物适应症' wtname,
'无适应症用药'title ,
'无诊断或无适应症用药' detail ,0,'适应症'

return
end

else if @icd10_codes is not null or @icd10_codes !=''
begin
select  @n_count =len(@icd10_codes)-len(REPLACE(@icd10_codes,';',''))+1

create table #tmp --创建临时表###tmp
(
    icd10_code varchar(50),
    diagnoses varchar(50)
);
--insert into #tmp values (@icd10_codes,'');

while @n_count1<@n_count
   begin
   if @icd10_codes not like '%;%'
   begin
   select @icd10_code =@icd10_codes
   select @n_count9=count(1) from t_icd10_base where icd_code =@icd10_code;
   if @n_count9 >0
   begin
   insert into #tmp select icd_code ,code from t_icd10_base where icd_code =@icd10_code;
   end
   else
   begin
   insert into #tmp select icd_code ,code from t_icd10_base where icd_code =substring(@icd10_code,1,3);
   end
   set @n_count1=@n_count1+1 ;
   end
   else
   begin
   select @icd10_code=substring(@icd10_codes,1,charindex(';',@icd10_codes)-1)
   select @icd10_codes=substring(@icd10_codes,charindex(';',@icd10_codes)+1,len(@icd10_codes)-charindex(';',@icd10_codes))
   select @n_count9=count(1) from t_icd10_base where icd_code =@icd10_code;
   if @n_count9 >0
    begin
   insert into #tmp select icd_code ,code from t_icd10_base where icd_code =@icd10_code;
   end
   else
   begin
   insert into #tmp select icd_code ,code from t_icd10_base where icd_code =substring(@icd10_code,1,3);
   end
   set @n_count1=@n_count1+1 ;
   end
end

select @n_count4=COUNT(1) from #tmp
if @n_count4 >0

begin

		DECLARE @his_code AS NVARCHAR(100)
		DECLARE cursor_his_code CURSOR FOR
		select DISTINCT his_code from t_pres_med where code =@code

		OPEN cursor_his_code
		FETCH NEXT FROM cursor_his_code INTO @his_code

		WHILE @@FETCH_STATUS = 0
		BEGIN

		select @n_count8=COUNT(1)from t_byyydzb a
        where  yp_code =@his_code
		DECLARE @med_name AS NVARCHAR(100)

		select top 1 @med_name= med_name from t_pres_med where code =@code and his_code=@his_code

		if   @n_count8 >0


		select distinct @sda_id=a.sda_id,@ywa_name=e.med_name,@icd10_name=c.icd_name,@remark=b.remark
		from t_pres_med E,t_byyydzb a,
		t_sda_icd10_info b,t_icd10_base c,#tmp d
		where  e.Code =@code
		and a.yp_code =e.his_code
		and  a.sda_id=b.sda_id
		and b.diagnoses=c.code
		and d.icd10_code =c.icd_code
		and b.bs  ='1';

		if  @sda_id <>'' or @sda_id is not null
		begin
		insert into #t_pres_fx_ls
		select @code,@ywa_name ywa,''ywb,'1' wtlvlcode,(select distinct d.wtlvl from wtlb d where d.wtlvlcode='0') wtlvl,
		'RLT012' wtcode,'ZDXGYWJJ'wtsp,'药物禁忌症' wtname,
		'药物禁忌症'title ,
		'【'+cast(tymc as varchar(100))+'】的禁忌症为：'+@icd10_name+';'+isnull(@remark,'')+'； 当诊断为('+@icd10_name+')时禁用【'+cast(tymc as varchar(100))+'】！' detail ,0,'适应症' from t_sda a
		where a.ID  =@sda_id;
		--return
		end
	   if  @sda_id ='' or @sda_id is  null
		begin
		select @n_count2=COUNT(1) from t_byyydzb a, t_sda_icd10_info b,t_icd10_base c,#tmp d
		where a.yp_code =@his_code
		and  a.sda_id=b.sda_id
		and b.diagnoses=c.code
		and substring(d.icd10_code,1,3) =substring(c.icd_code,1,3)
		and b.bs  ='5';

		select @n_count7=COUNT(1) from t_byyydzb a, t_sda_icd10_info b,t_icd10_base c
		where a.yp_code =@his_code
		and  a.sda_id=b.sda_id
		and b.diagnoses=c.code
		and b.bs ='5'

		SELECT @n_count18 =COUNT(1) from t_byyydzb a,t_sda_nosyz_lk b
		where A.sda_id=B.SDA_ID
		AND A.yp_code =@his_code


		if (@n_count2 =0 and @n_count7 >0 AND @n_count18=0)
		begin
		insert into #t_pres_fx_ls
		select @code,@med_name ywa,''ywb,'1'wtlvlcode,(select distinct d.wtlvl from wtlb d where d.wtlvlcode='1') wtlvl,
		'RLT037' wtcode,'ZDXGYWSY'wtsp,'诊断与药物适应症' wtname,
		'诊断与用药不适宜'title ,
		@med_name+'的适应症与患者诊断('+@zdxx+')不相关，可能存在超说明书用药或诊断不全。' detail ,0,'适应症'
		end
		end



		FETCH NEXT FROM cursor_his_code INTO @his_code
		END
		CLOSE cursor_his_code
		DEALLOCATE cursor_his_code

		insert into t_pres_fx select * from #t_pres_fx_ls

		select top 0 * from t_pres_fx
		return

end

end
end try
begin catch
select top 0 * from t_pres_fx
end catch








// MSSQL函数 get_zdxx_cj：
--
ALTER FUNCTION [dbo].[get_zdxx_cj](@text NVARCHAR(4000))
RETURNS VARCHAR(4000)
AS
BEGIN

--declare @text nvarchar(500)
--set @text='2,冠状动脉粥样硬化性心脏病,I25.103;2,2型糖尿病,E11.900;2,急性上呼吸道感染,J06.900;'
declare @r nvarchar(1000)
set @r=''
if(CHARINDEX(',',@text)=0 and @text<>'' and @text is not null)
begin
set @r=@text
return @r
end
declare @i nvarchar(1000)
declare @douhao_index int
declare @fenhao_index int
declare @first_douhao_index int
declare @last_douhao_index int
while(CHARINDEX(',',@text)>0)
begin
set @fenhao_index=CHARINDEX(';',@text)+1
--select @douhao_index
set @i= SUBSTRING(@text,0,@fenhao_index)
if (@i<>'')
begin
set @first_douhao_index=CHARINDEX(',',@i)
set @last_douhao_index= LEN(@i) - CHARINDEX(',', REVERSE(@i)) + 1
--select @i
set @text=REPLACE(@text,@i,'')
--select @text
--set @r=@r+SUBSTRING(@i,@first_douhao_index+1,iif((@last_douhao_index-3)<0,0,(@last_douhao_index-3)))+'　'
if @last_douhao_index-3 <0
begin
set @r=@r+SUBSTRING(@i,@first_douhao_index+1,0)+'、'
end
else
begin
set @r=@r+SUBSTRING(@i,@first_douhao_index+1,@last_douhao_index-3)+'、'
end

end
else
begin
set @r=@text
break
end
end
return @r
end

// MySQL函数 get_zdxx_cj：
CREATE FUNCTION `rms_get_zdxx_cj`(p_text TEXT)
RETURNS TEXT
READS SQL DATA
DETERMINISTIC
COMMENT '提取诊断信息并格式化'
BEGIN
    DECLARE v_result TEXT DEFAULT '';
    DECLARE v_work_text TEXT;
    DECLARE v_segment TEXT;
    DECLARE v_fenhao_index INT;
    DECLARE v_first_douhao_index INT;
    DECLARE v_last_douhao_index INT;
    DECLARE v_extract_length INT;
    DECLARE v_extracted_text TEXT;

    -- 处理空值或空字符串
    IF p_text IS NULL OR p_text = '' THEN
        RETURN '';
    END IF;

    -- 如果没有逗号，直接返回原文本
    IF LOCATE(',', p_text) = 0 THEN
        RETURN p_text;
    END IF;

    SET v_work_text = p_text;

    -- 循环处理每个分号分隔的段落
    WHILE LOCATE(',', v_work_text) > 0 DO
        -- 查找分号位置
        SET v_fenhao_index = LOCATE(';', v_work_text);

        IF v_fenhao_index > 0 THEN
            -- 提取到分号的部分（包含分号）
            SET v_segment = SUBSTRING(v_work_text, 1, v_fenhao_index);

            IF v_segment != '' THEN
                -- 查找第一个逗号位置
                SET v_first_douhao_index = LOCATE(',', v_segment);

                -- 查找最后一个逗号位置（通过反向查找实现）
                SET v_last_douhao_index = CHAR_LENGTH(v_segment) - LOCATE(',', REVERSE(v_segment)) + 1;

                -- 移除已处理的段落
                SET v_work_text = SUBSTRING(v_work_text, v_fenhao_index + 1);

                -- 计算提取长度
                SET v_extract_length = v_last_douhao_index - v_first_douhao_index - 1;

                -- 提取诊断名称部分
                IF v_extract_length > 0 THEN
                    SET v_extracted_text = SUBSTRING(v_segment, v_first_douhao_index + 1, v_extract_length);
                    SET v_result = CONCAT(v_result, v_extracted_text, '、');
                ELSE
                    SET v_result = CONCAT(v_result, '、');
                END IF;
            ELSE
                -- 如果段落为空，退出循环
                SET v_result = v_work_text;
                LEAVE;
            END IF;
        ELSE
            -- 没有找到分号，处理剩余部分
            LEAVE;
        END IF;
    END WHILE;

    -- 移除末尾的顿号
    IF CHAR_LENGTH(v_result) > 0 AND RIGHT(v_result, 1) = '、' THEN
        SET v_result = LEFT(v_result, CHAR_LENGTH(v_result) - 1);
    END IF;

    RETURN v_result;
END

// MySQL存储过程：
CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_syz`(
		IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),  -- 该参数无用，仅用来补位
    IN p_icd10_codes VARCHAR(500)
)
    COMMENT '适应症分析存储过程'
main_block: BEGIN
		DECLARE v_sda_id VARCHAR(10);
		DECLARE v_diagnoses VARCHAR(10);
		DECLARE v_n_count INT;
		DECLARE v_n_count1 INT DEFAULT 0;
		DECLARE v_n_count2 INT;
		DECLARE v_n_count4 INT;
		DECLARE v_n_count6 INT;
		DECLARE v_n_count7 INT;
		DECLARE v_n_count8 INT;
		DECLARE v_n_count9 INT;
		DECLARE v_n_count12 INT;
		DECLARE v_n_count13 INT;
		DECLARE v_n_count133 INT;
		DECLARE v_n_count144 INT;
		DECLARE v_n_count18 INT;
		DECLARE v_icd10_code VARCHAR(20);
		DECLARE v_hosp_flag VARCHAR(20);
		DECLARE v_zdxx VARCHAR(500);
		DECLARE v_icd10_name VARCHAR(500);
		DECLARE v_remark VARCHAR(500);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_his_code VARCHAR(100);
		DECLARE v_med_name VARCHAR(100);
		DECLARE v_icd10_codes_work VARCHAR(500);

		-- 游标变量
		DECLARE done INT DEFAULT FALSE;
		DECLARE his_code_cursor CURSOR FOR
				SELECT DISTINCT his_code FROM rms_t_pres_med WHERE code = p_code;
		DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;

		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				-- 如果出现异常，继续执行
				BEGIN END;
		END;

		-- 检查是否有中药
		SELECT COUNT(1) INTO v_n_count6
		FROM rms_t_pres_med a
		INNER JOIN rms_ITF_HOS_DRUG b ON b.DRUG_CODE = a.his_code
		WHERE a.Code = p_code AND b.ZX_FLAG = '3';

		IF v_n_count6 > 0 THEN
				LEAVE main_block;
		END IF;

		-- 获取住院标志和诊断信息
		SELECT hosp_flag, dia_info INTO v_hosp_flag, v_zdxx
		FROM rms_t_pres
		WHERE code = p_code LIMIT 1;

		-- 住院患者跳过
		IF v_hosp_flag = 'ip' THEN
				LEAVE main_block;
		END IF;

		-- 检查是否所有药品都在适应症白名单中
		SELECT COUNT(DISTINCT his_code) INTO v_n_count13
		FROM rms_t_pres_med
		WHERE code = p_code;

		SELECT COUNT(DISTINCT a.his_code) INTO v_n_count12
		FROM rms_t_pres_med a, rms_t_byyydzb b, rms_t_sda_nosyz_lk c
		WHERE a.code = p_code
				AND a.his_code = b.yp_code
				AND b.sda_id = c.sda_id;

		-- 如果所有药品都在白名单中，则跳过
		IF v_n_count13 = v_n_count12 THEN
				LEAVE main_block;
		END IF;

		-- 检查感染诊断与抗菌药物的匹配
		SELECT COUNT(1) INTO v_n_count133
		FROM rms_t_pres
		WHERE code = p_code
				AND (dia_info LIKE '%炎%' OR dia_info LIKE '%感染%');

		SELECT COUNT(1) INTO v_n_count144
		FROM rms_itf_hos_drug
		WHERE DRUG_CODE IN (
				SELECT his_code FROM rms_t_pres_med WHERE code = p_code
		) AND IS_ANTIBAC = '1';

		-- 如果有感染诊断且有抗菌药物，则跳过
		IF v_n_count133 > 0 AND v_n_count144 > 0 THEN
				LEAVE main_block;
		END IF;

		-- 清理之前的适应症分析结果
		DELETE FROM rms_t_pres_fx WHERE code = p_code AND wtcode = 'RLT037';

		-- 检查是否有诊断信息
		IF p_icd10_codes IS NULL OR p_icd10_codes = '' THEN
				INSERT INTO rms_t_pres_fx
				SELECT
						p_code,
						'' AS ywa,
						'' AS ywb,
						'1' AS wtlvlcode,
						(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = '2') AS wtlvl,
						'RLT037' AS wtcode,
						'ZDXGYWSY' AS wtsp,
						'诊断与药物适应症' AS wtname,
						'无适应症用药' AS title,
						'无诊断或无适应症用药' AS detail,
						0,
						'适应症';
				LEAVE main_block;
		END IF;

		-- 如果有诊断信息，进行适应症分析
		IF p_icd10_codes IS NOT NULL AND p_icd10_codes != '' THEN
				-- 计算诊断代码的数量
				SET v_n_count = CHAR_LENGTH(p_icd10_codes) - CHAR_LENGTH(REPLACE(p_icd10_codes, ';', '')) + 1;

				-- 创建临时表存储解析的诊断代码
				DROP TEMPORARY TABLE IF EXISTS temp_icd10_codes;
				CREATE TEMPORARY TABLE temp_icd10_codes (
						icd10_code VARCHAR(50),
						diagnoses VARCHAR(50)
				);

				-- 解析诊断代码字符串
				SET v_icd10_codes_work = p_icd10_codes;
				SET v_n_count1 = 0;

				WHILE v_n_count1 < v_n_count DO
						IF v_icd10_codes_work NOT LIKE '%;%' THEN
								SET v_icd10_code = v_icd10_codes_work;

								SELECT COUNT(1) INTO v_n_count9
								FROM rms_t_icd10_base
								WHERE icd_code = v_icd10_code;

								IF v_n_count9 > 0 THEN
										INSERT INTO temp_icd10_codes
										SELECT icd_code, code
										FROM rms_t_icd10_base
										WHERE icd_code = v_icd10_code;
								ELSE
										INSERT INTO temp_icd10_codes
										SELECT icd_code, code
										FROM rms_t_icd10_base
										WHERE icd_code = SUBSTRING(v_icd10_code, 1, 3);
								END IF;

								SET v_n_count1 = v_n_count1 + 1;
						ELSE
								SET v_icd10_code = SUBSTRING_INDEX(v_icd10_codes_work, ';', 1);
								SET v_icd10_codes_work = SUBSTRING(v_icd10_codes_work, LOCATE(';', v_icd10_codes_work) + 1);

								SELECT COUNT(1) INTO v_n_count9
								FROM rms_t_icd10_base
								WHERE icd_code = v_icd10_code;

								IF v_n_count9 > 0 THEN
										INSERT INTO temp_icd10_codes
										SELECT icd_code, code
										FROM rms_t_icd10_base
										WHERE icd_code = v_icd10_code;
								ELSE
										INSERT INTO temp_icd10_codes
										SELECT icd_code, code
										FROM rms_t_icd10_base
										WHERE icd_code = SUBSTRING(v_icd10_code, 1, 3);
								END IF;

								SET v_n_count1 = v_n_count1 + 1;
						END IF;
				END WHILE;

				SELECT COUNT(1) INTO v_n_count4 FROM temp_icd10_codes;

				IF v_n_count4 > 0 THEN
						-- 创建临时表存储分析结果
						DROP TEMPORARY TABLE IF EXISTS temp_pres_fx_ls;
						CREATE TEMPORARY TABLE temp_pres_fx_ls (
								Code VARCHAR(50),
								ywa VARCHAR(50),
								ywb VARCHAR(50),
								wtlvlcode INT,
								wtlvl VARCHAR(5),
								wtcode VARCHAR(8),
								wtsp VARCHAR(30),
								wtname VARCHAR(30),
								title VARCHAR(500),
								detail VARCHAR(1000),
								flag VARCHAR(2),
								text VARCHAR(20)
						);

						-- 打开游标遍历每个药品
						OPEN his_code_cursor;

						read_loop: LOOP
								FETCH his_code_cursor INTO v_his_code;
								IF done THEN
										LEAVE read_loop;
								END IF;

								SELECT COUNT(1) INTO v_n_count8
								FROM rms_t_byyydzb a
								WHERE yp_code = v_his_code;

								SELECT med_name INTO v_med_name
								FROM rms_t_pres_med
								WHERE code = p_code AND his_code = v_his_code
								LIMIT 1;

								IF v_n_count8 > 0 THEN
										-- 检查禁忌症
										SELECT DISTINCT
												a.sda_id, e.med_name, c.icd_name, b.remark
										INTO v_sda_id, v_ywa_name, v_icd10_name, v_remark
										FROM rms_t_pres_med e, rms_t_byyydzb a,
												rms_t_sda_icd10_info b, rms_t_icd10_base c, temp_icd10_codes d
										WHERE e.Code = p_code
												AND a.yp_code = e.his_code
												AND a.sda_id = b.sda_id
												AND b.diagnoses = c.code
												AND d.icd10_code = c.icd_code
												AND b.bs = '1'
										LIMIT 1;

										IF v_sda_id IS NOT NULL AND v_sda_id != '' THEN
												INSERT INTO temp_pres_fx_ls
												SELECT
														p_code,
														v_ywa_name AS ywa,
														'' AS ywb,
														'1' AS wtlvlcode,
														(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = '0') AS wtlvl,
														'RLT012' AS wtcode,
														'ZDXGYWJJ' AS wtsp,
														'药物禁忌症' AS wtname,
														'药物禁忌症' AS title,
														CONCAT('【', CAST(a.tymc AS CHAR), '】的禁忌症为：', v_icd10_name, ';',
																IFNULL(v_remark, ''), '； 当诊断为(', v_icd10_name, ')时禁用【',
																CAST(a.tymc AS CHAR), '】！') AS detail,
														0,
														'适应症'
												FROM rms_t_sda a
												WHERE a.ID = v_sda_id;
										END IF;

										IF v_sda_id IS NULL OR v_sda_id = '' THEN
												-- 检查适应症匹配
												SELECT COUNT(1) INTO v_n_count2
												FROM rms_t_byyydzb a, rms_t_sda_icd10_info b, rms_t_icd10_base c, temp_icd10_codes d
												WHERE a.yp_code = v_his_code
														AND a.sda_id = b.sda_id
														AND b.diagnoses = c.code
														AND SUBSTRING(d.icd10_code, 1, 3) = SUBSTRING(c.icd_code, 1, 3)
														AND b.bs = '5';

												SELECT COUNT(1) INTO v_n_count7
												FROM rms_t_byyydzb a, rms_t_sda_icd10_info b, rms_t_icd10_base c
												WHERE a.yp_code = v_his_code
														AND a.sda_id = b.sda_id
														AND b.diagnoses = c.code
														AND b.bs = '5';

												SELECT COUNT(1) INTO v_n_count18
												FROM rms_t_byyydzb a, rms_t_sda_nosyz_lk b
												WHERE a.sda_id = b.SDA_ID
														AND a.yp_code = v_his_code;

												IF (v_n_count2 = 0 AND v_n_count7 > 0 AND v_n_count18 = 0) THEN
														INSERT INTO temp_pres_fx_ls
														SELECT
																p_code,
																v_med_name AS ywa,
																'' AS ywb,
																'1' AS wtlvlcode,
																(SELECT DISTINCT d.wtlvl FROM rms_wtlb d WHERE d.wtlvlcode = '1') AS wtlvl,
																'RLT037' AS wtcode,
																'ZDXGYWSY' AS wtsp,
																'诊断与药物适应症' AS wtname,
																'诊断与用药不适宜' AS title,
																CONCAT(v_med_name, '的适应症与患者诊断(', v_zdxx, ')不相关，可能存在超说明书用药或诊断不全。') AS detail,
																0,
																'适应症';
												END IF;
										END IF;

										-- 重置变量
										SET v_sda_id = NULL;
								END IF;

						END LOOP;

						CLOSE his_code_cursor;

						-- 插入分析结果
						INSERT INTO rms_t_pres_fx
						SELECT * FROM temp_pres_fx_ls;

						-- 清理临时表
						DROP TEMPORARY TABLE temp_pres_fx_ls;
				END IF;

				-- 清理临时表
				DROP TEMPORARY TABLE temp_icd10_codes;
		END IF;

END
